"""
增强版特征质量分析工具
基于pyqlab现有框架，针对ag数据进行全面的特征质量评估
"""

import os
import pandas as pd
import numpy as np
import warnings
from typing import Dict, List, Tuple, Optional
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_regression
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

warnings.filterwarnings('ignore')

class EnhancedFeatureQualityAnalyzer:
    """增强版特征质量分析器"""
    
    def __init__(self, data_dir: str = 'f:/featdata/ag/', output_dir: str = './feature_analysis_results/'):
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 质量评估配置
        self.quality_config = {
            'missing_rate_threshold': 0.3,      # 缺失率阈值
            'unique_ratio_threshold': 0.95,     # 单一值占比阈值
            'zero_variance_threshold': 1e-8,    # 零方差阈值
            'outlier_std_threshold': 5.0,       # 异常值标准差阈值
            'correlation_threshold': 0.95,      # 高相关性阈值
            'stability_threshold': 0.1,         # 稳定性阈值
            'mutual_info_threshold': 0.01       # 互信息阈值
        }
        
        # 分析结果存储
        self.analysis_results = {}
        self.feature_scores = {}
        self.recommendations = {}
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """加载所有parquet文件"""
        print("🔄 加载数据文件...")
        data_files = {}
        
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.parquet'):
                file_path = os.path.join(self.data_dir, filename)
                try:
                    df = pd.read_parquet(file_path)
                    data_files[filename] = df
                    print(f"  ✅ {filename}: {df.shape}")
                except Exception as e:
                    print(f"  ❌ {filename}: 加载失败 - {e}")
        
        print(f"总共加载 {len(data_files)} 个文件")
        return data_files
    
    def basic_quality_analysis(self, df: pd.DataFrame, filename: str) -> Dict:
        """基础质量分析"""
        print(f"📊 基础质量分析: {filename}")
        
        total_rows, total_cols = df.shape
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        # 移除非特征列
        feature_cols = [col for col in numeric_cols if col not in ['date']]
        
        analysis = {
            'filename': filename,
            'total_rows': total_rows,
            'total_cols': total_cols,
            'feature_cols': len(feature_cols),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
            'missing_analysis': {},
            'variance_analysis': {},
            'distribution_analysis': {},
            'outlier_analysis': {}
        }
        
        # 缺失值分析
        missing_stats = {}
        for col in feature_cols:
            missing_count = df[col].isnull().sum()
            missing_rate = missing_count / total_rows
            missing_stats[col] = {
                'count': int(missing_count),
                'rate': float(missing_rate),
                'qualified': missing_rate <= self.quality_config['missing_rate_threshold']
            }
        analysis['missing_analysis'] = missing_stats
        
        # 方差分析
        variance_stats = {}
        for col in feature_cols:
            if col in df.columns:
                col_var = df[col].var()
                col_std = df[col].std()
                variance_stats[col] = {
                    'variance': float(col_var) if not pd.isna(col_var) else 0.0,
                    'std': float(col_std) if not pd.isna(col_std) else 0.0,
                    'zero_variance': col_var < self.quality_config['zero_variance_threshold']
                }
        analysis['variance_analysis'] = variance_stats
        
        # 分布分析
        distribution_stats = {}
        for col in feature_cols[:50]:  # 限制分析前50个特征以节省时间
            if col in df.columns and not df[col].isnull().all():
                col_data = df[col].dropna()
                if len(col_data) > 0:
                    # 单一值占比
                    value_counts = col_data.value_counts(normalize=True)
                    top_ratio = value_counts.iloc[0] if len(value_counts) > 0 else 1.0
                    
                    # 基本统计
                    distribution_stats[col] = {
                        'unique_count': int(col_data.nunique()),
                        'top_value_ratio': float(top_ratio),
                        'mean': float(col_data.mean()),
                        'median': float(col_data.median()),
                        'skewness': float(stats.skew(col_data)),
                        'kurtosis': float(stats.kurtosis(col_data)),
                        'qualified': top_ratio <= self.quality_config['unique_ratio_threshold']
                    }
        analysis['distribution_analysis'] = distribution_stats
        
        return analysis
    
    def correlation_analysis(self, df: pd.DataFrame, filename: str) -> Dict:
        """相关性分析"""
        print(f"🔗 相关性分析: {filename}")
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date']]
        
        # 限制特征数量以避免内存问题
        if len(feature_cols) > 100:
            feature_cols = feature_cols[:100]
            print(f"  限制分析前100个特征")
        
        correlation_analysis = {
            'filename': filename,
            'high_correlation_pairs': [],
            'correlation_matrix_stats': {},
            'redundant_features': []
        }
        
        try:
            # 计算相关性矩阵
            df_features = df[feature_cols].dropna()
            if len(df_features) > 0:
                corr_matrix = df_features.corr()
                
                # 找出高相关性特征对
                high_corr_pairs = []
                for i in range(len(corr_matrix.columns)):
                    for j in range(i+1, len(corr_matrix.columns)):
                        corr_val = corr_matrix.iloc[i, j]
                        if abs(corr_val) > self.quality_config['correlation_threshold']:
                            high_corr_pairs.append({
                                'feature1': corr_matrix.columns[i],
                                'feature2': corr_matrix.columns[j],
                                'correlation': float(corr_val)
                            })
                
                correlation_analysis['high_correlation_pairs'] = high_corr_pairs
                correlation_analysis['correlation_matrix_stats'] = {
                    'mean_abs_correlation': float(corr_matrix.abs().mean().mean()),
                    'max_correlation': float(corr_matrix.abs().max().max()),
                    'high_corr_pairs_count': len(high_corr_pairs)
                }
                
                # 识别冗余特征
                redundant_features = set()
                for pair in high_corr_pairs:
                    # 简单策略：保留第一个特征，标记第二个为冗余
                    redundant_features.add(pair['feature2'])
                
                correlation_analysis['redundant_features'] = list(redundant_features)
                
        except Exception as e:
            print(f"  ⚠️ 相关性分析失败: {e}")
            correlation_analysis['error'] = str(e)
        
        return correlation_analysis
    
    def stability_analysis(self, df: pd.DataFrame, filename: str) -> Dict:
        """稳定性分析"""
        print(f"📈 稳定性分析: {filename}")
        
        if 'date' not in df.columns:
            return {'error': '缺少date列，无法进行稳定性分析'}
        
        # 按时间分段分析
        df_sorted = df.sort_values('date')
        total_rows = len(df_sorted)
        
        # 分为4个时间段
        segment_size = total_rows // 4
        segments = []
        for i in range(4):
            start_idx = i * segment_size
            end_idx = (i + 1) * segment_size if i < 3 else total_rows
            segments.append(df_sorted.iloc[start_idx:end_idx])
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date']]
        
        stability_results = {
            'filename': filename,
            'segment_stats': [],
            'unstable_features': [],
            'stability_scores': {}
        }
        
        # 计算每个时间段的统计信息
        segment_stats = []
        for i, segment in enumerate(segments):
            stats_dict = {}
            for col in feature_cols[:50]:  # 限制分析特征数量
                if col in segment.columns and not segment[col].isnull().all():
                    col_data = segment[col].dropna()
                    if len(col_data) > 0:
                        stats_dict[col] = {
                            'mean': float(col_data.mean()),
                            'std': float(col_data.std()),
                            'median': float(col_data.median())
                        }
            segment_stats.append({
                'segment': i + 1,
                'start_date': int(segment['date'].min()) if 'date' in segment.columns else None,
                'end_date': int(segment['date'].max()) if 'date' in segment.columns else None,
                'stats': stats_dict
            })
        
        stability_results['segment_stats'] = segment_stats
        
        # 计算稳定性评分
        stability_scores = {}
        unstable_features = []
        
        for col in feature_cols[:50]:
            means = []
            stds = []
            for segment_stat in segment_stats:
                if col in segment_stat['stats']:
                    means.append(segment_stat['stats'][col]['mean'])
                    stds.append(segment_stat['stats'][col]['std'])
            
            if len(means) >= 2:
                # 计算均值的变异系数作为稳定性指标
                mean_cv = np.std(means) / (np.mean(np.abs(means)) + 1e-8)
                std_cv = np.std(stds) / (np.mean(stds) + 1e-8)
                
                stability_score = 1.0 / (1.0 + mean_cv + std_cv)
                stability_scores[col] = float(stability_score)
                
                if stability_score < (1.0 - self.quality_config['stability_threshold']):
                    unstable_features.append(col)
        
        stability_results['stability_scores'] = stability_scores
        stability_results['unstable_features'] = unstable_features
        
        return stability_results

    def target_correlation_analysis(self, df: pd.DataFrame, filename: str, target_col: str = 'change') -> Dict:
        """目标变量相关性分析"""
        print(f"🎯 目标相关性分析: {filename}")

        if target_col not in df.columns:
            return {'error': f'目标列 {target_col} 不存在'}

        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date', target_col]]

        target_corr_results = {
            'filename': filename,
            'target_column': target_col,
            'correlations': {},
            'mutual_info_scores': {},
            'low_correlation_features': [],
            'high_value_features': []
        }

        # 清理数据
        df_clean = df[feature_cols + [target_col]].dropna()
        if len(df_clean) == 0:
            return {'error': '清理后数据为空'}

        target_data = df_clean[target_col]

        # 计算皮尔逊相关系数
        correlations = {}
        for col in feature_cols[:100]:  # 限制特征数量
            if col in df_clean.columns:
                try:
                    corr_val = df_clean[col].corr(target_data)
                    if not pd.isna(corr_val):
                        correlations[col] = float(corr_val)
                except:
                    correlations[col] = 0.0

        target_corr_results['correlations'] = correlations

        # 计算互信息
        try:
            feature_data = df_clean[feature_cols[:50]]  # 限制特征数量
            mi_scores = mutual_info_regression(feature_data, target_data, random_state=42)
            mutual_info_dict = dict(zip(feature_data.columns, mi_scores))
            target_corr_results['mutual_info_scores'] = {k: float(v) for k, v in mutual_info_dict.items()}
        except Exception as e:
            print(f"  ⚠️ 互信息计算失败: {e}")
            target_corr_results['mutual_info_scores'] = {}

        # 识别低相关性特征
        low_corr_features = []
        high_value_features = []

        for col, corr_val in correlations.items():
            if abs(corr_val) < 0.01:  # 极低相关性
                low_corr_features.append(col)
            elif abs(corr_val) > 0.1:  # 较高相关性
                high_value_features.append({
                    'feature': col,
                    'correlation': corr_val,
                    'abs_correlation': abs(corr_val)
                })

        # 按绝对相关性排序
        high_value_features.sort(key=lambda x: x['abs_correlation'], reverse=True)

        target_corr_results['low_correlation_features'] = low_corr_features
        target_corr_results['high_value_features'] = high_value_features[:20]  # 取前20个

        return target_corr_results

    def outlier_analysis(self, df: pd.DataFrame, filename: str) -> Dict:
        """异常值分析"""
        print(f"🚨 异常值分析: {filename}")

        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date']]

        outlier_results = {
            'filename': filename,
            'outlier_stats': {},
            'high_outlier_features': []
        }

        for col in feature_cols[:100]:  # 限制分析特征数量
            if col in df.columns:
                col_data = df[col].dropna()
                if len(col_data) > 0:
                    # 使用IQR方法检测异常值
                    Q1 = col_data.quantile(0.25)
                    Q3 = col_data.quantile(0.75)
                    IQR = Q3 - Q1

                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR

                    outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                    outlier_rate = len(outliers) / len(col_data)

                    # 使用Z-score方法
                    z_scores = np.abs(stats.zscore(col_data))
                    z_outliers = col_data[z_scores > self.quality_config['outlier_std_threshold']]
                    z_outlier_rate = len(z_outliers) / len(col_data)

                    outlier_stats = {
                        'iqr_outliers': int(len(outliers)),
                        'iqr_outlier_rate': float(outlier_rate),
                        'z_outliers': int(len(z_outliers)),
                        'z_outlier_rate': float(z_outlier_rate),
                        'lower_bound': float(lower_bound),
                        'upper_bound': float(upper_bound),
                        'qualified': outlier_rate < 0.05  # 异常值比例小于5%
                    }

                    outlier_results['outlier_stats'][col] = outlier_stats

                    if outlier_rate > 0.1:  # 异常值比例超过10%
                        outlier_results['high_outlier_features'].append(col)

        return outlier_results

    def calculate_feature_scores(self, all_analyses: Dict) -> Dict:
        """计算特征综合评分"""
        print("🏆 计算特征综合评分...")

        feature_scores = {}

        # 收集所有特征名
        all_features = set()
        for analysis_type, analyses in all_analyses.items():
            for filename, analysis in analyses.items():
                if analysis_type == 'basic_quality':
                    all_features.update(analysis.get('missing_analysis', {}).keys())
                elif analysis_type == 'target_correlation':
                    all_features.update(analysis.get('correlations', {}).keys())

        # 为每个特征计算综合评分
        for feature in all_features:
            scores = {
                'missing_score': 1.0,
                'variance_score': 1.0,
                'distribution_score': 1.0,
                'correlation_score': 0.0,
                'stability_score': 1.0,
                'outlier_score': 1.0
            }

            # 缺失值评分
            missing_rates = []
            for filename, analysis in all_analyses.get('basic_quality', {}).items():
                if feature in analysis.get('missing_analysis', {}):
                    missing_rates.append(analysis['missing_analysis'][feature]['rate'])

            if missing_rates:
                avg_missing_rate = np.mean(missing_rates)
                scores['missing_score'] = max(0.0, 1.0 - avg_missing_rate / self.quality_config['missing_rate_threshold'])

            # 方差评分
            variances = []
            for filename, analysis in all_analyses.get('basic_quality', {}).items():
                if feature in analysis.get('variance_analysis', {}):
                    var_info = analysis['variance_analysis'][feature]
                    if not var_info['zero_variance']:
                        variances.append(var_info['std'])

            if variances:
                scores['variance_score'] = 1.0 if np.mean(variances) > self.quality_config['zero_variance_threshold'] else 0.0

            # 目标相关性评分
            correlations = []
            for filename, analysis in all_analyses.get('target_correlation', {}).items():
                if feature in analysis.get('correlations', {}):
                    correlations.append(abs(analysis['correlations'][feature]))

            if correlations:
                avg_corr = np.mean(correlations)
                scores['correlation_score'] = min(1.0, avg_corr * 10)  # 放大相关性评分

            # 稳定性评分
            stability_scores = []
            for filename, analysis in all_analyses.get('stability', {}).items():
                if feature in analysis.get('stability_scores', {}):
                    stability_scores.append(analysis['stability_scores'][feature])

            if stability_scores:
                scores['stability_score'] = np.mean(stability_scores)

            # 异常值评分
            outlier_rates = []
            for filename, analysis in all_analyses.get('outlier', {}).items():
                if feature in analysis.get('outlier_stats', {}):
                    outlier_rates.append(analysis['outlier_stats'][feature]['iqr_outlier_rate'])

            if outlier_rates:
                avg_outlier_rate = np.mean(outlier_rates)
                scores['outlier_score'] = max(0.0, 1.0 - avg_outlier_rate / 0.1)  # 10%异常值为临界点

            # 计算综合评分（加权平均）
            weights = {
                'missing_score': 0.2,
                'variance_score': 0.15,
                'distribution_score': 0.1,
                'correlation_score': 0.3,
                'stability_score': 0.15,
                'outlier_score': 0.1
            }

            total_score = sum(scores[key] * weights[key] for key in weights.keys())

            feature_scores[feature] = {
                'total_score': float(total_score),
                'component_scores': {k: float(v) for k, v in scores.items()},
                'recommendation': 'KEEP' if total_score >= 0.6 else 'FILTER' if total_score < 0.3 else 'REVIEW'
            }

        return feature_scores

    def run_comprehensive_analysis(self) -> Dict:
        """运行全面的特征质量分析"""
        print("🚀 开始全面特征质量分析...")

        # 加载数据
        data_files = self.load_all_data()
        if not data_files:
            raise ValueError("未找到数据文件")

        # 存储所有分析结果
        all_analyses = {
            'basic_quality': {},
            'correlation': {},
            'stability': {},
            'target_correlation': {},
            'outlier': {}
        }

        # 对每个文件进行分析
        for filename, df in data_files.items():
            print(f"\n📁 分析文件: {filename}")

            # 基础质量分析
            try:
                basic_analysis = self.basic_quality_analysis(df, filename)
                all_analyses['basic_quality'][filename] = basic_analysis
            except Exception as e:
                print(f"  ❌ 基础质量分析失败: {e}")

            # 相关性分析
            try:
                corr_analysis = self.correlation_analysis(df, filename)
                all_analyses['correlation'][filename] = corr_analysis
            except Exception as e:
                print(f"  ❌ 相关性分析失败: {e}")

            # 稳定性分析
            try:
                stability_analysis = self.stability_analysis(df, filename)
                all_analyses['stability'][filename] = stability_analysis
            except Exception as e:
                print(f"  ❌ 稳定性分析失败: {e}")

            # 目标相关性分析
            try:
                target_analysis = self.target_correlation_analysis(df, filename)
                all_analyses['target_correlation'][filename] = target_analysis
            except Exception as e:
                print(f"  ❌ 目标相关性分析失败: {e}")

            # 异常值分析
            try:
                outlier_analysis = self.outlier_analysis(df, filename)
                all_analyses['outlier'][filename] = outlier_analysis
            except Exception as e:
                print(f"  ❌ 异常值分析失败: {e}")

        # 计算特征评分
        feature_scores = self.calculate_feature_scores(all_analyses)

        # 存储结果
        self.analysis_results = all_analyses
        self.feature_scores = feature_scores

        return {
            'analyses': all_analyses,
            'feature_scores': feature_scores,
            'summary': self.generate_summary()
        }

    def generate_summary(self) -> Dict:
        """生成分析摘要"""
        if not self.feature_scores:
            return {}

        total_features = len(self.feature_scores)
        keep_features = sum(1 for score in self.feature_scores.values() if score['recommendation'] == 'KEEP')
        filter_features = sum(1 for score in self.feature_scores.values() if score['recommendation'] == 'FILTER')
        review_features = sum(1 for score in self.feature_scores.values() if score['recommendation'] == 'REVIEW')

        # 获取最佳和最差特征
        sorted_features = sorted(self.feature_scores.items(), key=lambda x: x[1]['total_score'], reverse=True)
        best_features = sorted_features[:10]
        worst_features = sorted_features[-10:]

        summary = {
            'total_features': total_features,
            'recommendations': {
                'KEEP': keep_features,
                'FILTER': filter_features,
                'REVIEW': review_features
            },
            'recommendation_rates': {
                'KEEP': keep_features / total_features if total_features > 0 else 0,
                'FILTER': filter_features / total_features if total_features > 0 else 0,
                'REVIEW': review_features / total_features if total_features > 0 else 0
            },
            'best_features': [{'feature': name, 'score': data['total_score']} for name, data in best_features],
            'worst_features': [{'feature': name, 'score': data['total_score']} for name, data in worst_features],
            'average_score': np.mean([score['total_score'] for score in self.feature_scores.values()]),
            'score_distribution': {
                'high_quality': sum(1 for score in self.feature_scores.values() if score['total_score'] >= 0.8),
                'medium_quality': sum(1 for score in self.feature_scores.values() if 0.4 <= score['total_score'] < 0.8),
                'low_quality': sum(1 for score in self.feature_scores.values() if score['total_score'] < 0.4)
            }
        }

        return summary

    def save_results(self, results: Dict, output_prefix: str = 'ag_feature_analysis'):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果
        full_results_file = os.path.join(self.output_dir, f'{output_prefix}_full_{timestamp}.json')
        with open(full_results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 完整结果已保存: {full_results_file}")

        # 保存特征评分CSV
        scores_df = pd.DataFrame([
            {
                'feature': feature,
                'total_score': data['total_score'],
                'recommendation': data['recommendation'],
                'missing_score': data['component_scores']['missing_score'],
                'variance_score': data['component_scores']['variance_score'],
                'correlation_score': data['component_scores']['correlation_score'],
                'stability_score': data['component_scores']['stability_score'],
                'outlier_score': data['component_scores']['outlier_score']
            }
            for feature, data in self.feature_scores.items()
        ])

        scores_csv_file = os.path.join(self.output_dir, f'{output_prefix}_scores_{timestamp}.csv')
        scores_df.to_csv(scores_csv_file, index=False, encoding='utf-8-sig')
        print(f"✅ 特征评分已保存: {scores_csv_file}")

        # 保存推荐特征列表
        recommendations = {
            'KEEP': [f for f, data in self.feature_scores.items() if data['recommendation'] == 'KEEP'],
            'FILTER': [f for f, data in self.feature_scores.items() if data['recommendation'] == 'FILTER'],
            'REVIEW': [f for f, data in self.feature_scores.items() if data['recommendation'] == 'REVIEW']
        }

        recommendations_file = os.path.join(self.output_dir, f'{output_prefix}_recommendations_{timestamp}.json')
        with open(recommendations_file, 'w', encoding='utf-8') as f:
            json.dump(recommendations, f, ensure_ascii=False, indent=2)
        print(f"✅ 推荐列表已保存: {recommendations_file}")

        return {
            'full_results': full_results_file,
            'scores_csv': scores_csv_file,
            'recommendations': recommendations_file
        }

    def generate_detailed_report(self, results: Dict) -> str:
        """生成详细的分析报告"""
        summary = results.get('summary', {})

        report = f"""
# AG特征数据质量分析报告

## 分析概览

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据目录**: {self.data_dir}
**总特征数**: {summary.get('total_features', 0)}

## 质量评估摘要

### 推荐分布
- **保留特征 (KEEP)**: {summary.get('recommendations', {}).get('KEEP', 0)} ({summary.get('recommendation_rates', {}).get('KEEP', 0):.1%})
- **过滤特征 (FILTER)**: {summary.get('recommendations', {}).get('FILTER', 0)} ({summary.get('recommendation_rates', {}).get('FILTER', 0):.1%})
- **需审查特征 (REVIEW)**: {summary.get('recommendations', {}).get('REVIEW', 0)} ({summary.get('recommendation_rates', {}).get('REVIEW', 0):.1%})

### 质量分布
- **高质量特征 (≥0.8)**: {summary.get('score_distribution', {}).get('high_quality', 0)}
- **中等质量特征 (0.4-0.8)**: {summary.get('score_distribution', {}).get('medium_quality', 0)}
- **低质量特征 (<0.4)**: {summary.get('score_distribution', {}).get('low_quality', 0)}

**平均质量评分**: {summary.get('average_score', 0):.3f}

## 最佳特征 (Top 10)

"""

        best_features = summary.get('best_features', [])
        for i, feature_info in enumerate(best_features, 1):
            report += f"{i}. **{feature_info['feature']}** - 评分: {feature_info['score']:.3f}\n"

        report += f"""

## 最差特征 (Bottom 10)

"""

        worst_features = summary.get('worst_features', [])
        for i, feature_info in enumerate(worst_features, 1):
            report += f"{i}. **{feature_info['feature']}** - 评分: {feature_info['score']:.3f}\n"

        report += f"""

## 分析方法说明

### 评估维度
1. **缺失值分析**: 检查特征的缺失率，阈值: {self.quality_config['missing_rate_threshold']}
2. **方差分析**: 检测零方差或低方差特征
3. **分布分析**: 分析特征值分布的合理性
4. **目标相关性**: 评估特征与目标变量的相关性
5. **稳定性分析**: 检验特征在不同时间段的稳定性
6. **异常值分析**: 检测异常值比例

### 评分机制
- **总评分**: 各维度加权平均 (0-1分)
- **推荐标准**:
  - KEEP: 总评分 ≥ 0.6
  - REVIEW: 0.3 ≤ 总评分 < 0.6
  - FILTER: 总评分 < 0.3

## 使用建议

### 高质量特征 (推荐保留)
建议在模型训练中优先使用评分较高的特征，这些特征具有：
- 较低的缺失率
- 合理的方差
- 与目标变量有一定相关性
- 时序稳定性好
- 异常值比例可控

### 需要过滤的特征
以下类型的特征建议过滤：
- 缺失率过高 (>{self.quality_config['missing_rate_threshold']})
- 零方差或接近零方差
- 与目标变量相关性极低
- 时序不稳定
- 异常值比例过高

### 需要审查的特征
中等评分的特征需要进一步分析：
- 可能需要特征工程处理
- 可能在特定条件下有用
- 建议结合业务知识判断

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report


def main():
    """主函数"""
    print("🎯 AG特征数据质量分析工具")
    print("=" * 50)

    # 创建分析器
    analyzer = EnhancedFeatureQualityAnalyzer(
        data_dir='f:/featdata/ag/',
        output_dir='./feature_analysis_results/'
    )

    try:
        # 运行全面分析
        results = analyzer.run_comprehensive_analysis()

        # 保存结果
        saved_files = analyzer.save_results(results)

        # 生成详细报告
        detailed_report = analyzer.generate_detailed_report(results)

        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(analyzer.output_dir, f'ag_feature_analysis_report_{timestamp}.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(detailed_report)

        print(f"\n🎉 分析完成!")
        print(f"📊 详细报告: {report_file}")
        print(f"📈 特征评分: {saved_files['scores_csv']}")
        print(f"📋 推荐列表: {saved_files['recommendations']}")
        print(f"💾 完整结果: {saved_files['full_results']}")

        # 打印摘要
        summary = results.get('summary', {})
        print(f"\n📋 分析摘要:")
        print(f"  总特征数: {summary.get('total_features', 0)}")
        print(f"  推荐保留: {summary.get('recommendations', {}).get('KEEP', 0)}")
        print(f"  建议过滤: {summary.get('recommendations', {}).get('FILTER', 0)}")
        print(f"  需要审查: {summary.get('recommendations', {}).get('REVIEW', 0)}")
        print(f"  平均评分: {summary.get('average_score', 0):.3f}")

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
