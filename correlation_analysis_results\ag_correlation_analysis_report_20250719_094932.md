
# AG特征相关性和多重共线性分析报告

## 分析概览

**分析时间**: 2025-07-19 09:49:32
**数据目录**: f:/featdata/ag/
**样本数量**: 3,239
**分析特征数**: 227

## 相关性分析结果

### 基本统计
- **特征对总数**: 25,651
- **高相关性对数** (≥0.95): 242
- **中等相关性对数** (≥0.7): 2613
- **平均绝对相关性**: 0.308
- **最大相关性**: 1.000

### 高相关性特征对 (Top 20)
1. **CMO_1** ↔ **RSI_1**: 1.000
2. **AD_1** ↔ **AD_2**: 1.000
3. **BAND_BK_BARS_1** ↔ **BAND_BK_BARS_2**: 1.000
4. **BAND_EXPAND_1** ↔ **BAND_EXPAND_2**: 1.000
5. **BAND_GAP_1** ↔ **BAND_GAP_2**: 1.000
6. **BAND_GRADIENT_THRESHOLD_1** ↔ **BAND_GRADIENT_THRESHOLD_2**: 1.000
7. **BAND_WIDTH_1** ↔ **BAND_WIDTH_2**: 1.000
8. **BAR_STICK_LENGTH_1** ↔ **BAR_STICK_LENGTH_2**: 1.000
9. **CLOSE_1** ↔ **NEW_1**: 1.000
10. **CLOSE_2** ↔ **NEW_2**: 1.000
11. **HIGH_1** ↔ **HIGH_2**: 1.000
12. **HYO_CROSS_BARS_1** ↔ **HYO_CROSS_BARS_2**: 1.000
13. **LR_SLOPE_FAST_THRESHOLD_1** ↔ **LR_SLOPE_FAST_THRESHOLD_2**: 1.000
14. **LR_SLOPE_SLOW_THRESHOLD_1** ↔ **LR_SLOPE_SLOW_THRESHOLD_2**: 1.000
15. **MOMENTUM_THRESHOLD_1** ↔ **MOMENTUM_THRESHOLD_2**: 1.000
16. **NEW_CHANGE_PERCENT_1** ↔ **NEW_CHANGE_PERCENT_2**: 1.000
17. **SQUEEZE_1** ↔ **SQUEEZE_2**: 1.000
18. **SQUEEZE_GAP_THRESHOLD_1** ↔ **SQUEEZE_GAP_THRESHOLD_2**: 1.000
19. **SQUEEZE_SIGNAL_1** ↔ **SQUEEZE_SIGNAL_2**: 1.000
20. **SQUEEZE_ZERO_BARS_1** ↔ **SQUEEZE_ZERO_BARS_2**: 1.000


## 冗余特征分析

### 冗余特征组
发现 **109** 个冗余特征，分布在 **44** 个特征组中。

**组 1** (共3个特征):
- 保留: `BAND_POSITION_1`
- 移除: `CMO_1`, `RSI_1`

**组 2** (共2个特征):
- 保留: `AD_2`
- 移除: `AD_1`

**组 3** (共2个特征):
- 保留: `BAND_BK_BARS_1`
- 移除: `BAND_BK_BARS_2`

**组 4** (共2个特征):
- 保留: `BAND_EXPAND_2`
- 移除: `BAND_EXPAND_1`

**组 5** (共7个特征):
- 保留: `CMO_2`
- 移除: `LR_SLOPE_MIDD_2`, `TRIX_FAST_2`, `RSI_2`, `APO_2`, `BAND_GAP_1`, `BAND_GAP_2`

**组 6** (共2个特征):
- 保留: `BAND_GRADIENT_THRESHOLD_1`
- 移除: `BAND_GRADIENT_THRESHOLD_2`

**组 7** (共2个特征):
- 保留: `BAND_WIDTH_1`
- 移除: `BAND_WIDTH_2`

**组 8** (共2个特征):
- 保留: `BAR_STICK_LENGTH_1`
- 移除: `BAR_STICK_LENGTH_2`

**组 9** (共51个特征):
- 保留: `DEMA_FAST_2`
- 移除: `SQUEEZE_KC_DWL_2`, `MACD_DIFF_1`, `TEMA_SLOW_2`, `CLOSE_1`, `MACD_DEA_2`, `T3_FAST_2`, `EMA_SLOW_1`, `T3_SLOW_2`, `DEMA_SLOW_1`, `TRIMA_FAST_1`, `MA_SLOW_1`, `TEMA_SLOW_1`, `BOLL_MID_1`, `MAMA_FAST_1`, `EMA_SLOW_2`, `TRIMA_FAST_2`, `NEW_CHANGE_PERCENT_2`, `KAMA_FAST_2`, `TSF_1`, `KAMA_FAST_1`, `NEW_CHANGE_PERCENT_1`, `BAND_MDL_1`, `SQUEEZE_KC_UPL_2`, `TEMA_FAST_1`, `TRIMA_SLOW_2`, `MA_FAST_1`, `MACD_DIFF_2`, `BOLL_MID_2`, `MACD_DEA_1`, `BAND_MDL_2`, `CLOSE_2`, `MAMA_SLOW_2`, `EMA_FAST_1`, `EMA_FAST_2`, `TEMA_FAST_2`, `T3_FAST_1`, `MAMA_SLOW_1`, `T3_SLOW_1`, `MA_FAST_2`, `KAMA_SLOW_1`, `SQUEEZE_MDL_2`, `NEW_2`, `TSF_2`, `TRIMA_SLOW_1`, `DEMA_FAST_1`, `NEW_1`, `MAMA_FAST_2`, `SQUEEZE_MDL_1`, `MA_SLOW_2`, `DEMA_SLOW_2`

**组 10** (共2个特征):
- 保留: `HIGH_2`
- 移除: `HIGH_1`



## 多重共线性分析 (VIF)

### VIF统计
- **分析特征数**: 50
- **高VIF特征数** (>10.0): 41
- **平均VIF**: inf
- **最大VIF**: inf

### 高VIF特征 (Top 15)
1. **SQUEEZE_ZERO_BARS_1**: VIF = inf
2. **SQUEEZE_ZERO_BARS_2**: VIF = inf
3. **LR_SLOPE_SLOW_THRESHOLD_2**: VIF = inf
4. **LR_SLOPE_SLOW_THRESHOLD_1**: VIF = inf
5. **MA_SLOW_2**: VIF = inf
6. **SQUEEZE_MDL_2**: VIF = inf
7. **TL_THRESHOLD_1**: VIF = inf
8. **TL_THRESHOLD_2**: VIF = inf
9. **TREND_LOWEST_1**: VIF = inf
10. **TREND_LOWEST_2**: VIF = inf
11. **MOMENTUM_THRESHOLD_1**: VIF = inf
12. **MOMENTUM_THRESHOLD_2**: VIF = inf
13. **TREND_HLR_1**: VIF = inf
14. **TREND_HLR_2**: VIF = inf
15. **MOM_2**: VIF = inf


## 推荐建议

### 特征筛选建议

#### 1. 基于高相关性的特征筛选
- 建议移除 **109** 个冗余特征
- 这些特征与其他特征的相关性超过 0.95
- 移除后可以减少特征维度，提高模型训练效率

#### 2. 基于多重共线性的特征筛选
- 发现 **41** 个高VIF特征
- 建议优先移除VIF > 10.0 的特征


#### 3. 综合筛选策略
1. **优先级1**: 移除高相关性冗余特征
2. **优先级2**: 移除高VIF特征
3. **优先级3**: 结合业务知识进行最终筛选

### 特征工程建议
1. **主成分分析**: 对高相关性特征组进行PCA降维
2. **特征组合**: 将相关特征进行加权组合
3. **正则化**: 使用L1/L2正则化自动进行特征选择

## 质量保证

### 分析方法
- **相关性分析**: 使用皮尔逊相关系数
- **多重共线性**: 使用方差膨胀因子(VIF)
- **冗余检测**: 基于相关性阈值的图算法

### 分析限制
- 分析基于单个代表性数据文件
- VIF分析限制在前50个特征
- 采样分析可能不完全代表全量数据特征

---

*报告生成时间: 2025-07-19 09:49:32*
