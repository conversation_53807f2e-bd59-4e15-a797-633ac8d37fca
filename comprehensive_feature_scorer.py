"""
综合特征质量评分和筛选建议工具
整合所有分析结果，为每个特征生成最终的质量评分和筛选建议
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple

class ComprehensiveFeatureScorer:
    """综合特征质量评分器"""
    
    def __init__(self, 
                 feature_analysis_dir='./feature_analysis_results/',
                 correlation_analysis_dir='./correlation_analysis_results/',
                 stability_analysis_dir='./stability_analysis_results/',
                 output_dir='./comprehensive_analysis_results/'):
        
        self.feature_analysis_dir = feature_analysis_dir
        self.correlation_analysis_dir = correlation_analysis_dir
        self.stability_analysis_dir = stability_analysis_dir
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 评分权重配置
        self.scoring_weights = {
            'basic_quality': 0.25,      # 基础质量（缺失值、方差等）
            'target_correlation': 0.30, # 与目标变量的相关性
            'stability': 0.20,          # 时序稳定性
            'redundancy': 0.15,         # 冗余性（负向）
            'consistency': 0.10         # 文件类型一致性
        }
        
        # 筛选阈值
        self.thresholds = {
            'high_quality': 0.7,        # 高质量特征阈值
            'medium_quality': 0.4,      # 中等质量特征阈值
            'low_quality': 0.2          # 低质量特征阈值
        }
        
        self.results = {}
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_analysis_results(self):
        """加载所有分析结果"""
        print("🔄 加载分析结果...")
        
        results = {
            'feature_analysis': None,
            'correlation_analysis': None,
            'stability_analysis': None
        }
        
        # 加载特征质量分析结果
        try:
            feature_files = [f for f in os.listdir(self.feature_analysis_dir) if f.endswith('_scores_.csv')]
            if feature_files:
                latest_feature_file = sorted(feature_files)[-1]
                feature_scores_path = os.path.join(self.feature_analysis_dir, latest_feature_file)
                results['feature_analysis'] = pd.read_csv(feature_scores_path)
                print(f"  ✅ 特征质量分析: {latest_feature_file}")
        except Exception as e:
            print(f"  ❌ 特征质量分析加载失败: {e}")
        
        # 加载相关性分析结果
        try:
            corr_files = [f for f in os.listdir(self.correlation_analysis_dir) if f.endswith('_redundant_features_.json')]
            if corr_files:
                latest_corr_file = sorted(corr_files)[-1]
                corr_path = os.path.join(self.correlation_analysis_dir, latest_corr_file)
                with open(corr_path, 'r', encoding='utf-8') as f:
                    results['correlation_analysis'] = json.load(f)
                print(f"  ✅ 相关性分析: {latest_corr_file}")
        except Exception as e:
            print(f"  ❌ 相关性分析加载失败: {e}")
        
        # 加载稳定性分析结果
        try:
            stability_files = [f for f in os.listdir(self.stability_analysis_dir) if f.endswith('_full_.json')]
            if stability_files:
                latest_stability_file = sorted(stability_files)[-1]
                stability_path = os.path.join(self.stability_analysis_dir, latest_stability_file)
                with open(stability_path, 'r', encoding='utf-8') as f:
                    results['stability_analysis'] = json.load(f)
                print(f"  ✅ 稳定性分析: {latest_stability_file}")
        except Exception as e:
            print(f"  ❌ 稳定性分析加载失败: {e}")
        
        return results
    
    def calculate_comprehensive_scores(self, analysis_results):
        """计算综合评分"""
        print("🏆 计算综合特征评分...")
        
        # 获取所有特征列表
        all_features = set()
        
        if analysis_results['feature_analysis'] is not None:
            all_features.update(analysis_results['feature_analysis']['feature'].tolist())
        
        if analysis_results['correlation_analysis'] is not None:
            all_features.update(analysis_results['correlation_analysis']['redundant_features_list'])
        
        if (analysis_results['stability_analysis'] is not None and 
            'temporal_stability' in analysis_results['stability_analysis']):
            stability_scores = analysis_results['stability_analysis']['temporal_stability']['stability_scores']
            all_features.update(stability_scores.keys())
        
        print(f"  📊 总特征数: {len(all_features)}")
        
        # 为每个特征计算综合评分
        comprehensive_scores = {}
        
        for feature in all_features:
            scores = {
                'basic_quality_score': 0.5,      # 默认中等评分
                'target_correlation_score': 0.0, # 默认无相关性
                'stability_score': 0.5,          # 默认中等稳定性
                'redundancy_penalty': 0.0,       # 默认无冗余惩罚
                'consistency_score': 0.5         # 默认中等一致性
            }
            
            # 基础质量评分
            if analysis_results['feature_analysis'] is not None:
                feature_row = analysis_results['feature_analysis'][
                    analysis_results['feature_analysis']['feature'] == feature
                ]
                if not feature_row.empty:
                    scores['basic_quality_score'] = float(feature_row.iloc[0]['total_score'])
            
            # 冗余性惩罚
            if analysis_results['correlation_analysis'] is not None:
                redundant_features = analysis_results['correlation_analysis']['redundant_features_list']
                if feature in redundant_features:
                    scores['redundancy_penalty'] = 0.5  # 冗余特征惩罚
            
            # 稳定性评分
            if (analysis_results['stability_analysis'] is not None and 
                'temporal_stability' in analysis_results['stability_analysis']):
                stability_scores = analysis_results['stability_analysis']['temporal_stability']['stability_scores']
                if feature in stability_scores:
                    scores['stability_score'] = float(stability_scores[feature]['stability_score'])
            
            # 年度一致性评分
            if (analysis_results['stability_analysis'] is not None and 
                'year_over_year' in analysis_results['stability_analysis']):
                year_comparison = analysis_results['stability_analysis']['year_over_year']
                if 'significant_changes' in year_comparison:
                    significant_features = [item['feature'] for item in year_comparison['significant_changes']]
                    if feature in significant_features:
                        scores['consistency_score'] = 0.3  # 年度变化显著的特征一致性较低
            
            # 计算综合评分
            comprehensive_score = (
                scores['basic_quality_score'] * self.scoring_weights['basic_quality'] +
                scores['target_correlation_score'] * self.scoring_weights['target_correlation'] +
                scores['stability_score'] * self.scoring_weights['stability'] +
                (1.0 - scores['redundancy_penalty']) * self.scoring_weights['redundancy'] +
                scores['consistency_score'] * self.scoring_weights['consistency']
            )
            
            # 生成推荐
            if comprehensive_score >= self.thresholds['high_quality']:
                recommendation = 'KEEP_HIGH'
                recommendation_reason = '高质量特征，强烈推荐保留'
            elif comprehensive_score >= self.thresholds['medium_quality']:
                recommendation = 'KEEP_MEDIUM'
                recommendation_reason = '中等质量特征，建议保留'
            elif comprehensive_score >= self.thresholds['low_quality']:
                recommendation = 'REVIEW'
                recommendation_reason = '低质量特征，需要审查'
            else:
                recommendation = 'FILTER'
                recommendation_reason = '极低质量特征，建议过滤'
            
            comprehensive_scores[feature] = {
                'comprehensive_score': float(comprehensive_score),
                'component_scores': {k: float(v) for k, v in scores.items()},
                'recommendation': recommendation,
                'recommendation_reason': recommendation_reason,
                'quality_tier': self._get_quality_tier(comprehensive_score)
            }
        
        return comprehensive_scores
    
    def _get_quality_tier(self, score):
        """获取质量等级"""
        if score >= 0.8:
            return 'EXCELLENT'
        elif score >= 0.7:
            return 'GOOD'
        elif score >= 0.5:
            return 'FAIR'
        elif score >= 0.3:
            return 'POOR'
        else:
            return 'VERY_POOR'
    
    def generate_feature_recommendations(self, comprehensive_scores):
        """生成特征筛选推荐"""
        print("📋 生成特征筛选推荐...")
        
        # 按推荐类型分组
        recommendations = {
            'KEEP_HIGH': [],
            'KEEP_MEDIUM': [],
            'REVIEW': [],
            'FILTER': []
        }
        
        # 按质量等级分组
        quality_tiers = {
            'EXCELLENT': [],
            'GOOD': [],
            'FAIR': [],
            'POOR': [],
            'VERY_POOR': []
        }
        
        for feature, score_info in comprehensive_scores.items():
            recommendations[score_info['recommendation']].append({
                'feature': feature,
                'score': score_info['comprehensive_score'],
                'reason': score_info['recommendation_reason']
            })
            
            quality_tiers[score_info['quality_tier']].append({
                'feature': feature,
                'score': score_info['comprehensive_score']
            })
        
        # 排序
        for category in recommendations.values():
            category.sort(key=lambda x: x['score'], reverse=True)
        
        for tier in quality_tiers.values():
            tier.sort(key=lambda x: x['score'], reverse=True)
        
        # 生成统计信息
        total_features = len(comprehensive_scores)
        recommendation_stats = {
            category: len(features) for category, features in recommendations.items()
        }
        
        quality_stats = {
            tier: len(features) for tier, features in quality_tiers.items()
        }
        
        print(f"  📊 推荐统计:")
        print(f"    高质量保留: {recommendation_stats['KEEP_HIGH']}")
        print(f"    中等质量保留: {recommendation_stats['KEEP_MEDIUM']}")
        print(f"    需要审查: {recommendation_stats['REVIEW']}")
        print(f"    建议过滤: {recommendation_stats['FILTER']}")
        
        return {
            'recommendations': recommendations,
            'quality_tiers': quality_tiers,
            'statistics': {
                'total_features': total_features,
                'recommendation_stats': recommendation_stats,
                'quality_stats': quality_stats,
                'average_score': np.mean([s['comprehensive_score'] for s in comprehensive_scores.values()])
            }
        }

    def run_comprehensive_scoring(self):
        """运行综合评分分析"""
        print("🚀 开始综合特征质量评分...")

        # 加载分析结果
        analysis_results = self.load_analysis_results()

        # 计算综合评分
        comprehensive_scores = self.calculate_comprehensive_scores(analysis_results)

        # 生成推荐
        recommendations = self.generate_feature_recommendations(comprehensive_scores)

        # 整合结果
        final_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'scoring_config': {
                'weights': self.scoring_weights,
                'thresholds': self.thresholds
            },
            'comprehensive_scores': comprehensive_scores,
            'recommendations': recommendations,
            'data_sources': {
                'feature_analysis_available': analysis_results['feature_analysis'] is not None,
                'correlation_analysis_available': analysis_results['correlation_analysis'] is not None,
                'stability_analysis_available': analysis_results['stability_analysis'] is not None
            }
        }

        self.results = final_results
        return final_results

    def generate_final_report(self, results):
        """生成最终综合报告"""
        stats = results['recommendations']['statistics']

        report = f"""
# AG特征数据综合质量评估与筛选建议报告

## 执行摘要

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**总特征数**: {stats['total_features']}
**平均质量评分**: {stats['average_score']:.3f}

### 核心发现
- **高质量特征**: {stats['recommendation_stats']['KEEP_HIGH']} 个 ({stats['recommendation_stats']['KEEP_HIGH']/stats['total_features']*100:.1f}%)
- **中等质量特征**: {stats['recommendation_stats']['KEEP_MEDIUM']} 个 ({stats['recommendation_stats']['KEEP_MEDIUM']/stats['total_features']*100:.1f}%)
- **需审查特征**: {stats['recommendation_stats']['REVIEW']} 个 ({stats['recommendation_stats']['REVIEW']/stats['total_features']*100:.1f}%)
- **建议过滤特征**: {stats['recommendation_stats']['FILTER']} 个 ({stats['recommendation_stats']['FILTER']/stats['total_features']*100:.1f}%)

## 特征质量分布

### 按质量等级分布
- **卓越 (≥0.8)**: {stats['quality_stats']['EXCELLENT']} 个
- **良好 (0.7-0.8)**: {stats['quality_stats']['GOOD']} 个
- **一般 (0.5-0.7)**: {stats['quality_stats']['FAIR']} 个
- **较差 (0.3-0.5)**: {stats['quality_stats']['POOR']} 个
- **极差 (<0.3)**: {stats['quality_stats']['VERY_POOR']} 个

## 推荐特征列表

### 🏆 高质量特征 (强烈推荐保留)
"""

        # 高质量特征
        high_quality_features = results['recommendations']['recommendations']['KEEP_HIGH'][:20]
        for i, feature_info in enumerate(high_quality_features, 1):
            report += f"{i}. **{feature_info['feature']}** - 评分: {feature_info['score']:.3f}\n"

        if len(high_quality_features) > 20:
            report += f"... 还有 {len(results['recommendations']['recommendations']['KEEP_HIGH']) - 20} 个高质量特征\n"

        report += f"""

### ✅ 中等质量特征 (建议保留)
"""

        # 中等质量特征
        medium_quality_features = results['recommendations']['recommendations']['KEEP_MEDIUM'][:15]
        for i, feature_info in enumerate(medium_quality_features, 1):
            report += f"{i}. **{feature_info['feature']}** - 评分: {feature_info['score']:.3f}\n"

        if len(medium_quality_features) > 15:
            report += f"... 还有 {len(results['recommendations']['recommendations']['KEEP_MEDIUM']) - 15} 个中等质量特征\n"

        report += f"""

### ⚠️ 需审查特征
"""

        # 需审查特征
        review_features = results['recommendations']['recommendations']['REVIEW'][:10]
        for i, feature_info in enumerate(review_features, 1):
            report += f"{i}. **{feature_info['feature']}** - 评分: {feature_info['score']:.3f}\n"

        report += f"""

### 🗑️ 建议过滤特征
"""

        # 建议过滤特征
        filter_features = results['recommendations']['recommendations']['FILTER'][:10]
        for i, feature_info in enumerate(filter_features, 1):
            report += f"{i}. **{feature_info['feature']}** - 评分: {feature_info['score']:.3f}\n"

        report += f"""

## 评分方法说明

### 评分维度与权重
1. **基础质量** ({self.scoring_weights['basic_quality']*100:.0f}%): 缺失值、方差、分布合理性
2. **目标相关性** ({self.scoring_weights['target_correlation']*100:.0f}%): 与目标变量的相关性
3. **时序稳定性** ({self.scoring_weights['stability']*100:.0f}%): 不同时间段的稳定性
4. **冗余性** ({self.scoring_weights['redundancy']*100:.0f}%): 与其他特征的冗余程度（负向）
5. **一致性** ({self.scoring_weights['consistency']*100:.0f}%): 不同数据源间的一致性

### 筛选阈值
- **高质量保留**: ≥ {self.thresholds['high_quality']}
- **中等质量保留**: {self.thresholds['medium_quality']} - {self.thresholds['high_quality']}
- **需要审查**: {self.thresholds['low_quality']} - {self.thresholds['medium_quality']}
- **建议过滤**: < {self.thresholds['low_quality']}

## 实施建议

### 阶段一：立即实施
1. **保留高质量特征**: 优先使用 {stats['recommendation_stats']['KEEP_HIGH']} 个高质量特征进行模型训练
2. **过滤低质量特征**: 移除 {stats['recommendation_stats']['FILTER']} 个极低质量特征

### 阶段二：进一步优化
1. **评估中等质量特征**: 根据具体业务需求决定是否使用 {stats['recommendation_stats']['KEEP_MEDIUM']} 个中等质量特征
2. **审查问题特征**: 深入分析 {stats['recommendation_stats']['REVIEW']} 个需审查特征，考虑特征工程改进

### 阶段三：持续监控
1. **建立特征监控体系**: 定期评估特征质量变化
2. **动态调整特征集**: 根据模型表现和数据变化调整特征选择

## 预期效果

### 特征维度优化
- **原始特征数**: {stats['total_features']}
- **推荐保留特征数**: {stats['recommendation_stats']['KEEP_HIGH'] + stats['recommendation_stats']['KEEP_MEDIUM']}
- **维度减少**: {stats['recommendation_stats']['REVIEW'] + stats['recommendation_stats']['FILTER']} 个特征 ({(stats['recommendation_stats']['REVIEW'] + stats['recommendation_stats']['FILTER'])/stats['total_features']*100:.1f}%)

### 模型性能提升
- **数据质量提升**: 移除低质量特征，提高训练数据质量
- **训练效率提升**: 减少特征维度，加快模型训练速度
- **泛化能力增强**: 使用稳定、相关的特征，提高模型泛化能力

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*基于特征质量分析、相关性分析、稳定性分析的综合评估结果*
"""

        return report

    def save_results(self, results, output_prefix='ag_comprehensive_analysis'):
        """保存综合分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果
        full_results_file = os.path.join(self.output_dir, f'{output_prefix}_full_{timestamp}.json')
        with open(full_results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 完整结果已保存: {full_results_file}")

        # 保存特征评分CSV
        scores_data = []
        for feature, score_info in results['comprehensive_scores'].items():
            scores_data.append({
                'feature': feature,
                'comprehensive_score': score_info['comprehensive_score'],
                'recommendation': score_info['recommendation'],
                'quality_tier': score_info['quality_tier'],
                'basic_quality_score': score_info['component_scores']['basic_quality_score'],
                'target_correlation_score': score_info['component_scores']['target_correlation_score'],
                'stability_score': score_info['component_scores']['stability_score'],
                'redundancy_penalty': score_info['component_scores']['redundancy_penalty'],
                'consistency_score': score_info['component_scores']['consistency_score'],
                'recommendation_reason': score_info['recommendation_reason']
            })

        scores_df = pd.DataFrame(scores_data)
        scores_df = scores_df.sort_values('comprehensive_score', ascending=False)

        scores_csv_file = os.path.join(self.output_dir, f'{output_prefix}_scores_{timestamp}.csv')
        scores_df.to_csv(scores_csv_file, index=False, encoding='utf-8-sig')
        print(f"✅ 特征评分已保存: {scores_csv_file}")

        # 保存推荐列表
        recommendations_file = os.path.join(self.output_dir, f'{output_prefix}_recommendations_{timestamp}.json')
        recommendations_data = {
            'high_quality_features': [f['feature'] for f in results['recommendations']['recommendations']['KEEP_HIGH']],
            'medium_quality_features': [f['feature'] for f in results['recommendations']['recommendations']['KEEP_MEDIUM']],
            'review_features': [f['feature'] for f in results['recommendations']['recommendations']['REVIEW']],
            'filter_features': [f['feature'] for f in results['recommendations']['recommendations']['FILTER']],
            'statistics': results['recommendations']['statistics']
        }

        with open(recommendations_file, 'w', encoding='utf-8') as f:
            json.dump(recommendations_data, f, ensure_ascii=False, indent=2)
        print(f"✅ 推荐列表已保存: {recommendations_file}")

        return {
            'full_results': full_results_file,
            'scores_csv': scores_csv_file,
            'recommendations': recommendations_file
        }


def main():
    """主函数"""
    print("🎯 AG特征综合质量评估与筛选建议工具")
    print("=" * 60)

    # 创建综合评分器
    scorer = ComprehensiveFeatureScorer(
        feature_analysis_dir='./feature_analysis_results/',
        correlation_analysis_dir='./correlation_analysis_results/',
        stability_analysis_dir='./stability_analysis_results/',
        output_dir='./comprehensive_analysis_results/'
    )

    try:
        # 运行综合评分
        results = scorer.run_comprehensive_scoring()

        # 保存结果
        saved_files = scorer.save_results(results)

        # 生成最终报告
        final_report = scorer.generate_final_report(results)

        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(scorer.output_dir, f'ag_comprehensive_analysis_report_{timestamp}.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(final_report)

        print(f"\n🎉 综合分析完成!")
        print(f"📊 最终报告: {report_file}")
        print(f"📈 特征评分: {saved_files['scores_csv']}")
        print(f"📋 推荐列表: {saved_files['recommendations']}")
        print(f"💾 完整结果: {saved_files['full_results']}")

        # 打印关键统计信息
        stats = results['recommendations']['statistics']
        print(f"\n📋 综合评估摘要:")
        print(f"  总特征数: {stats['total_features']}")
        print(f"  平均评分: {stats['average_score']:.3f}")
        print(f"  高质量特征: {stats['recommendation_stats']['KEEP_HIGH']} ({stats['recommendation_stats']['KEEP_HIGH']/stats['total_features']*100:.1f}%)")
        print(f"  中等质量特征: {stats['recommendation_stats']['KEEP_MEDIUM']} ({stats['recommendation_stats']['KEEP_MEDIUM']/stats['total_features']*100:.1f}%)")
        print(f"  需审查特征: {stats['recommendation_stats']['REVIEW']} ({stats['recommendation_stats']['REVIEW']/stats['total_features']*100:.1f}%)")
        print(f"  建议过滤特征: {stats['recommendation_stats']['FILTER']} ({stats['recommendation_stats']['FILTER']/stats['total_features']*100:.1f}%)")

        # 推荐的最终特征集合
        recommended_features = (
            stats['recommendation_stats']['KEEP_HIGH'] +
            stats['recommendation_stats']['KEEP_MEDIUM']
        )
        print(f"\n🎯 推荐最终特征集合: {recommended_features} 个特征")
        print(f"   维度减少: {stats['total_features'] - recommended_features} 个特征 ({(stats['total_features'] - recommended_features)/stats['total_features']*100:.1f}%)")

    except Exception as e:
        print(f"❌ 综合分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
