@REM python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\qt_factors.py ^
@REM --dbfile d:/RoboQuant2/store/kv.db ^
@REM --key_prefix ffs ^
@REM --save_path F:/featdata/top ^
@REM --save_file top ^
@REM --batch_size 10000 ^
@REM --years [2025]

@REM python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\qt_factors.py ^
@REM --is_sf ^
@REM --dbfile d:/RoboQuant2/store/kv.db ^
@REM --key_prefix fsfs ^
@REM --save_path F:/featdata/sf ^
@REM --save_file sf ^
@REM --batch_size 10000 ^
@REM --years "[2025]"

@REM python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\qt_factors.py ^
@REM --is_sf ^
@REM --dbfile d:/RoboQuant2/store/kv.db ^
@REM --key_prefix fsfs ^
@REM --save_path F:/featdata/ic ^
@REM --save_file ic ^
@REM --batch_size 10000 ^
@REM --years "[2024, 2025]"

python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\qt_factors.py ^
--dbfile d:/RoboQuant2/store/kv.db ^
--key_prefix ffs ^
--save_path F:/featdata/ag ^
--save_file ag ^
--batch_size 10000 ^
--years "[2024, 2025]"

