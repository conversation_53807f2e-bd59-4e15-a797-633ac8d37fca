model_name: hierarchical_tcn
loss: huber
lr: 0.0001
model_path: pyqlab.models.fintimeseries
optimizer: adamw
lr_scheduler: cosine
weight_decay: 0.0001
num_classes: 3
years:
- 2025
start_date: '2025-01-01'
end_date: '2025-07-31'
direct: ls
is_filter_extreme: true
extreme_threshold: 5.0
is_normal: false
verbose: false
fut_codes:
- AG
- RB
- HC
- BU
- RU
- SP
- SC
- SS
- AO
- BR
- SR
- CF
- FG
- TA
- MA
- OI
- RM
- CY
- SF
- SM
- AP
- UR
- SA
- PK
- PX
- SH
- PR
- M
- 'Y'
- A
- P
- JM
- I
- C
- CS
- L
- V
- PP
- EG
- EB
data_path: f:/featdata/ag
fd_set:
- - 1
  - 1
- - 2
  - 1
sel_fd_names: SEL_FACTOR_NAMES
block_name: ag
batch_size: 64
num_workers: 0
seed: 179
version: HTCN-AG
mode: regression
num_total_codes: 100
code_embedding_dim: 4
num_periods: 2
feat_dim: 35
seq_len: 30
kernel_size: 3
tcn_channels:
- 128
- 256
tcn_output_dim: 256
dropout: 0.4
output_seq_features: 1
main_period_idx: 0
lr_decay_steps: 5
lr_decay_rate: 0.1
lr_decay_min_lr: 1.0e-07
nan_handling_strategy: skip
restart: false
max_epochs: 50
early_stop: 7
min_delta: 0.003
k_folds: 5
log_dir: lightning_logs
sub_dir: ''
model_dir: model
cv_type: none
export_onnx: false
best_score: 1.5
