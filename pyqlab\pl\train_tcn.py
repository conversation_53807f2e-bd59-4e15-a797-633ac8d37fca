
import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary

from pyqlab.models import PLData
from pyqlab.models import PLFtsModel

from pyqlab.data.data_api import get_dataset
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import KFold, TimeSeriesSplit
import os
import random
from time import time
from datetime import datetime
import numpy as np
from pyqlab.pl.utils import get_best_saved_model_filename
from collections import defaultdict

torch.autograd.set_detect_anomaly(True) # 放在 main 函数或脚本的开头

def get_trainer_name(args):
    if  "IF" in args.fut_codes:
        market="SF"
    else:
        market="FUT"
    return f"{market}_{args.version}_{args.seq_len}_{len(args.fd_set)}_{args.feat_dim}"
    # return f'{args.model_name}_{sum(args.tcn_channels)}'

    
def save_model_as_to_onnx(args, dataset):
    trainer_name=get_trainer_name(args)
    model_files = get_best_saved_model_filename(
        log_dir=args.log_dir,
        sub_dir=trainer_name
    )
    print(args.log_dir, trainer_name, model_files)
    if len(model_files) == 0:
        raise Exception("No saved model found!")
    print(f"=== Export best model files ===")
    for model_file, best_score in model_files.items():
        if best_score > args.best_score:
            print(f"Skip model file: {model_file}  {best_score}")
            continue
        else:
            print(f"Export model file: {model_file}  {best_score}")
        try:
            model = PLFtsModel.load_from_checkpoint(checkpoint_path=model_file)
            model.freeze()
            code_ids = torch.zeros(1,).to(torch.int32)
            example_input = torch.rand(1, len(args.fd_set), args.seq_len, args.feat_dim)
            trainer_name = get_trainer_name(args)
            tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
            model_name = f'{trainer_name}_{tm_str}_{best_score:.3f}_{args.direct}'
            output_path = f"{args.model_dir}/{model_name}.onnx"
            model.to_onnx(output_path, (code_ids, example_input), export_params=True)
            dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")
            print(f"\nModel saved to: {output_path}")

        except Exception as e:
            print(f"Error: {e}")
    
def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    return callbacks


def main(args):
    args.tcn_channels = eval(args.tcn_channels)
    args.years = eval(args.years)
    args.fut_codes = eval(args.fut_codes)
    args.fd_set = eval(args.fd_set)
    args.seed = random.randint(0, 1000)

    print(args)
    dataset = get_dataset(
        data_path=args.data_path,
        years=args.years,
        block_name=args.block_name,
        start_date=args.start_date,
        end_date=args.end_date,
        is_normal=args.is_normal,
        verbose=args.verbose,
        seq_len=args.seq_len,
        is_filter_extreme=args.is_filter_extreme,
        extreme_threshold=args.extreme_threshold,
        fd_set=args.fd_set,
        sel_fd_names=args.sel_fd_names,
        fut_codes=args.fut_codes,
    )
    if args.export_onnx:
        save_model_as_to_onnx(args, dataset)
        return
    
    code_data, x_data, y_data = dataset.prepare(
        direct=args.direct,
        )
    
    if args.feat_dim != dataset.get_feat_dim():
        print(f"Warning: args.feat_dim={args.feat_dim} does not match dataset.get_feat_dim()={dataset.get_feat_dim()}.")
        args.feat_dim = dataset.get_feat_dim()

    pl.seed_everything(args.seed)

    # 创建回调函数
    callbacks = load_callbacks(args)

    full_dataset = TensorDataset(torch.tensor(code_data), torch.tensor(x_data), torch.tensor(y_data))
    fold_metrics = []
    best_model_paths = []

    # 选择分割方式
    if args.cv_type == 'none':
        # 1. 找到每个证券代码对应的样本索引
        code_to_indices = defaultdict(list)
        for idx, code in enumerate(code_data):
            code_to_indices[code.item() if hasattr(code, 'item') else code].append(idx)
        print(code_to_indices.keys())

        # 2. 对每个代码的样本做8:2划分
        train_idx, val_idx = [], []
        for indices in code_to_indices.values():
            n = len(indices)
            n_train = int(n * 0.8)
            random.shuffle(indices)
            train_idx.extend(indices[:n_train])
            val_idx.extend(indices[n_train:])

        # 3. 合并所有代码的训练/验证索引
        splits = [(train_idx, val_idx)]
        print("=== 单次训练（无交叉验证） ===")
    elif args.cv_type == 'kfold':
        kfold = KFold(n_splits=args.k_folds, shuffle=True, random_state=args.seed)
        splits = list(kfold.split(full_dataset))
        print(f"=== KFold 交叉验证: {args.k_folds} 折 ===")
    elif args.cv_type == 'tscv':
        tscv = TimeSeriesSplit(n_splits=args.k_folds)
        splits = list(tscv.split(full_dataset))
        print(f"=== 时间序列交叉验证: {args.k_folds} 折 ===")
    else:
        raise ValueError(f"未知的cv_type: {args.cv_type}")

    for fold, (train_idx, val_idx) in enumerate(splits):
        print(f"=== Training fold {fold} ===")
        train_data = Subset(full_dataset, train_idx)
        val_data = Subset(full_dataset, val_idx)
        data_module = PLData(train_data, val_data, batch_size=args.batch_size, num_workers=args.num_workers, seed=args.seed)

        # ## MODIFICATION 2: 修正K-Fold核心逻辑
        # 在每个fold开始时，都创建一个全新的、未训练过的模型实例。
        # 这确保了每个fold的训练都是独立的，这才是交叉验证的正确做法。
        # 之前的代码会加载上一个fold训练好的模型，这是错误的。
        print("--- Initializing a new model for this fold ---")
        model = PLFtsModel(**vars(args))

        # 2. 为每个fold配置回调和日志
        callbacks = load_callbacks(args)
        # 日志保存在每个fold的子目录中，避免混淆
        logger = TensorBoardLogger(
            save_dir=args.log_dir, 
            name=get_trainer_name(args),
            version=f'fold_{fold}'
        )

        # ## MODIFICATION 3: 优化Trainer，自动使用GPU
        # accelerator='auto' 会自动检测并使用可用的GPU, TPU, HPU等。
        # devices='auto' 会自动使用所有可用的设备。
        # 这比硬编码gpus=1更灵活和推荐。
        trainer = Trainer(
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
            gradient_clip_val=1.0,
            accelerator='auto',
            devices='auto',
        )
        trainer.fit(model, data_module)
        best_val_loss = trainer.callback_metrics.get('val_loss') 
        if best_val_loss is None:
             best_val_loss = callbacks[1].best_model_score
        if best_val_loss is not None:
            print(f"--- Fold {fold} finished. Best validation loss: {best_val_loss:.4f} ---")
            fold_metrics.append(best_val_loss.item())
            best_model_paths.append(callbacks[1].best_model_path)
        else:
            print(f"--- Fold {fold} finished. Could not retrieve validation loss. ---")

    # --- 总结和输出最终结果 ---
    if args.cv_type != 'none':
        print(f"\n{'='*20} Cross-Validation Summary {'='*20}")
        if fold_metrics:
            mean_loss = np.mean(fold_metrics)
            std_loss = np.std(fold_metrics)
            print(f"Average validation loss across {args.k_folds} folds: {mean_loss:.4f}")
            print(f"Standard deviation of validation loss: {std_loss:.4f}")
            best_fold_idx = np.argmin(fold_metrics)
            best_loss = fold_metrics[best_fold_idx]
            print(f"Best performance was in fold {best_fold_idx} with val_loss: {best_loss:.4f}")
            best_model_path = best_model_paths[best_fold_idx]
            best_model_log_dir = os.path.join(args.log_dir, get_trainer_name(args), f'fold_{best_fold_idx}', 'checkpoints')
            best_model_files = [f for f in os.listdir(best_model_log_dir) if f.startswith('best-')]
            if best_model_files:
                best_model_path = os.path.join(best_model_log_dir, best_model_files[0])
                print(f"Loading best model from path: {best_model_path}")
                model = PLFtsModel.load_from_checkpoint(checkpoint_path=best_model_path)
                model.freeze()
                code_ids = torch.zeros(1,).to(torch.int32)
                example_input = torch.rand(1, len(args.fd_set), args.seq_len, args.feat_dim)
                trainer_name = get_trainer_name(args)
                tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
                model_name = f'{trainer_name}_{tm_str}_{best_loss:.3f}_{args.direct}'
                output_path = f"{args.model_dir}/{model_name}.onnx"
                model.to_onnx(output_path, (code_ids, example_input), export_params=True)
                dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")
                print(f"\nModel from best fold saved to: {output_path}")
    else:
        print(f"\n{'='*20} Training Summary {'='*20}")
        if fold_metrics:
            best_loss = fold_metrics[0]
            print(f"Validation loss: {best_loss:.4f}")
            best_model_path = best_model_paths[0]
            best_model_log_dir = os.path.join(args.log_dir, get_trainer_name(args), f'fold_0', 'checkpoints')
            best_model_files = [f for f in os.listdir(best_model_log_dir) if f.startswith('best-')]
            if best_model_files:
                best_model_path = os.path.join(best_model_log_dir, best_model_files[0])
                print(f"Loading best model from path: {best_model_path}")
                model = PLFtsModel.load_from_checkpoint(checkpoint_path=best_model_path)
                model.freeze()
                code_ids = torch.zeros(1,).to(torch.int32)
                example_input = torch.rand(1, len(args.fd_set), args.seq_len, args.feat_dim)
                trainer_name = get_trainer_name(args)
                tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
                model_name = f'{trainer_name}_{tm_str}_{best_loss:.3f}_{args.direct}'
                output_path = f"{args.model_dir}/{model_name}.onnx"
                model.to_onnx(output_path, (code_ids, example_input), export_params=True)
                dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")
                print(f"\nModel saved to: {output_path}")
    print("\n=== Training Process Finished ===")


if __name__ == '__main__':
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--years', default='["2025"]', type=str)
    parser.add_argument('--start_date', default='', type=str)
    parser.add_argument('--end_date', default='', type=str)
    parser.add_argument('--direct', default='ls', choices=['ls', 'lsh', 'long', 'short'], type=str)
    parser.add_argument('--is_filter_extreme', action='store_true')
    parser.add_argument('--extreme_threshold', default=3.0, type=float)
    parser.add_argument('--is_normal', action='store_true')
    parser.add_argument('--verbose', action='store_true')
    parser.add_argument('--fut_codes', default='MAIN_SEL_FUT_CODES', choices=['MAIN_FUT_CODES', 'MAIN_SEL_FUT_CODES', 'SF_FUT_CODES'], type=str)
    parser.add_argument('--data_path', default='f:/featdata', type=str)
    parser.add_argument('--fd_set', default='[(1,1), (1,0), (2,0), (2,1)]', type=str)
    parser.add_argument('--sel_fd_names', default='SEL_FACTOR_NAMES', choices=['SEL_FACTOR_NAMES', 'SEL_FACTOR_NAMES2', 'SEL_FACTOR_NAMES3'], type=str)
    parser.add_argument('--block_name', default='top', choices=['main', 'ag', 'ic', 'top', 'hot', 'acc','sf', 'hs300', 'zz500', 'zz1000'], type=str)

    # Data module ===========================
    parser.add_argument('--batch_size', default=256, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)

    # Model Hyperparameters =================
    parser.add_argument('--version', default='TCN', type=str)
    parser.add_argument('--model_name', default='tcn_model', type=str)
    parser.add_argument('--model_path', default='pyqlab.models.fintimeseries', type=str)
    parser.add_argument('--loss', default='mse', choices=['mse', 'l1', 'smooth_l1', 'huber', 'bce', 'bce_with_logits', 'ce', 'nll'], type=str)
    parser.add_argument('--lr', default=1e-5, type=float)

    # model
    parser.add_argument('--mode', default='classification', choices=['classification', 'regression'], type=str)
    parser.add_argument('--num_total_codes', default=100, type=int)
    parser.add_argument('--code_embedding_dim', default=16, type=int)
    parser.add_argument('--num_periods', default=4, type=int)
    parser.add_argument('--feat_dim', default=49, type=int)
    parser.add_argument('--seq_len', default=15, type=int)
    parser.add_argument('--kernel_size', default=3, type=int)
    parser.add_argument('--tcn_channels', default='[64, 128, 256]', type=str)
    parser.add_argument('--tcn_output_dim', default=256, type=int)
    parser.add_argument('--dropout', default=0.2, type=float)
    parser.add_argument('--output_seq_features', default=1, type=int)
    parser.add_argument('--main_period_idx', default=1, type=int)
    # parser.add_argument('--pooling', default='max', choices=['max', 'avg'], type=str)
    # 

    # LR Scheduler
    # lr_scheduler 可以有以下选项：
    #     'step'：使用步进式学习率调度器
    #     'cosine'：使用余弦退火学习率调度器
    #     'plateau'：使用学习率衰减调度器
    #     'exponential'：使用指数衰减学习率调度器
    #     'reduce_on_plateau'：使用学习率衰减调度器，并在验证集上监测指标不再改善时降低学习率
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=7, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-7, type=float)
    
    parser.add_argument('--optimizer', default='adamw', choices=['adam', 'sgd', 'adamw'], type=str)
    parser.add_argument('--weight_decay', default=1e-5, type=float)
    parser.add_argument('--nan_handling_strategy', default='skip', type=str, choices=['skip', 'replace', 'stop'])

    # Restart Control
    parser.add_argument('--restart', action='store_true')

    # Training Info
    parser.add_argument('--max_epochs', default=50, type=int)
    parser.add_argument('--early_stop', default=10, type=int)
    parser.add_argument('--min_delta', default=1e-6, type=float) 
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    parser.add_argument('--sub_dir', default='', type=str)
    
    # Others
    parser.add_argument('--model_dir', default='model', type=str)
    # 新增交叉验证类型参数
    parser.add_argument('--cv_type', default='tscv', choices=['none', 'kfold', 'tscv'], type=str, help='交叉验证类型: none=不做CV, kfold=普通KFold, tscv=时间序列CV')
    # Reset Some Default Trainer Arguments' Default Values
    # parser.set_defaults(max_epochs=10)
    parser.add_argument('--export_onnx', action='store_true')
    parser.add_argument('--best_score', default=1.5, type=float)
    args = parser.parse_args()

    main(args)
