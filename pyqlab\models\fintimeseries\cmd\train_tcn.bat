e:
cd e:\lab\RoboQuant\pylab

@REM --fd_set="[(1,0), (1,1), (2,0), (2,1)]" ^
@REM --sel_fd_names=SEL_FACTOR_NAMES2 ^
@REM --feat_dim= 35 52 ^

@rem --version="HTCN-SF" ^
@rem --data_path="f:/featdata/sf" ^
@rem --fut_codes=SF_FUT_CODES ^
@rem --block_name=sf ^

@rem --version="HTCN" ^
@rem --data_path="f:/featdata/top" ^
@rem --fut_codes=MAIN_SEL_FUT_CODES ^
@rem --block_name=top ^

python ./pyqlab/pl/train_tcn.py ^
--version="HTCN-AG" ^
--data_path="f:/featdata/ag" ^
--fut_codes=MAIN_SEL_FUT_CODES ^
--block_name=ag ^
--years="[2025]" ^
--start_date=2024-01-01 ^
--end_date=2025-07-31 ^
--fd_set="[(1,1), (2,1)]" ^
--num_periods=2 ^
--main_period_idx=0 ^
--is_filter_extreme ^
--extreme_threshold=5.0 ^
--seq_len=30 ^
--direct=ls ^
--batch_size=128 ^
--k_folds=5 ^
--tcn_channels="[128, 256]" ^
--tcn_output_dim=256 ^
--sel_fd_names=SEL_FACTOR_NAMES2 ^
--feat_dim=52 ^
--code_embedding_dim=1 ^
--kernel_size=3 ^
--model_name=hierarchical_tcn ^
--model_path=pyqlab.models.fintimeseries ^
--mode=regression ^
--loss=mse ^
--dropout=0.4 ^
--early_stop=7 ^
--min_delta=0.003 ^
--weight_decay=1e-4 ^
--max_epochs=50 ^
--lr_scheduler=cosine ^
--lr_decay_steps=5 ^
--lr_decay_rate=0.1 ^
--lr=1e-4 ^
--cv_type=none
@REM rem  ^
@REM rem --best_score=1.2 ^
@REM rem --export_onnx
@REM --verbose
