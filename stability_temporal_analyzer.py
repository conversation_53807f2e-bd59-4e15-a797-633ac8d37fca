"""
特征稳定性和时序一致性分析工具
专门分析AG数据中特征在不同时间段的稳定性和一致性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
import warnings
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

warnings.filterwarnings('ignore')

class StabilityTemporalAnalyzer:
    """稳定性和时序一致性分析器"""
    
    def __init__(self, data_dir='f:/featdata/ag/', output_dir='./stability_analysis_results/'):
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 分析配置
        self.config = {
            'stability_threshold': 0.1,        # 稳定性阈值
            'temporal_segments': 4,            # 时间段数量
            'min_samples_per_segment': 1000,   # 每个时间段最小样本数
            'statistical_significance': 0.05,  # 统计显著性水平
            'max_features_for_analysis': 100   # 分析的最大特征数
        }
        
        self.results = {}
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_temporal_data(self):
        """加载时序数据"""
        print("🔄 加载时序数据...")
        
        # 加载2024和2025年的数据进行对比分析
        data_files = {}
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.parquet'):
                file_path = os.path.join(self.data_dir, filename)
                df = pd.read_parquet(file_path)
                
                # 添加年份信息
                if '2024' in filename:
                    df['year'] = 2024
                elif '2025' in filename:
                    df['year'] = 2025
                else:
                    continue
                
                # 添加文件类型信息
                if 'fd_1_0' in filename:
                    df['file_type'] = 'fd_1_0'
                elif 'fd_1_1' in filename:
                    df['file_type'] = 'fd_1_1'
                elif 'fd_2_0' in filename:
                    df['file_type'] = 'fd_2_0'
                elif 'fd_2_1' in filename:
                    df['file_type'] = 'fd_2_1'
                elif 'fd_6_0' in filename:
                    df['file_type'] = 'fd_6_0'
                elif 'fd_6_1' in filename:
                    df['file_type'] = 'fd_6_1'
                else:
                    df['file_type'] = 'unknown'
                
                data_files[filename] = df
                print(f"  ✅ {filename}: {df.shape}")
        
        # 合并所有数据
        all_data = pd.concat(data_files.values(), ignore_index=True)
        all_data['datetime'] = pd.to_datetime(all_data['date'], unit='s')
        all_data = all_data.sort_values(['datetime', 'file_type'])
        
        print(f"  📊 合并后数据形状: {all_data.shape}")
        print(f"  📅 时间范围: {all_data['datetime'].min()} - {all_data['datetime'].max()}")
        
        return all_data
    
    def temporal_stability_analysis(self, df):
        """时序稳定性分析"""
        print("📈 执行时序稳定性分析...")
        
        # 获取数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date', 'year']]
        
        # 限制分析特征数量
        if len(feature_cols) > self.config['max_features_for_analysis']:
            # 选择方差最大的特征
            feature_vars = df[feature_cols].var().sort_values(ascending=False)
            feature_cols = feature_vars.head(self.config['max_features_for_analysis']).index.tolist()
            print(f"  📉 限制分析特征数: {len(feature_cols)}")
        
        # 按时间分段分析
        df_sorted = df.sort_values('datetime')
        total_rows = len(df_sorted)
        segment_size = total_rows // self.config['temporal_segments']
        
        segments = []
        for i in range(self.config['temporal_segments']):
            start_idx = i * segment_size
            end_idx = (i + 1) * segment_size if i < self.config['temporal_segments'] - 1 else total_rows
            segment = df_sorted.iloc[start_idx:end_idx]
            
            if len(segment) >= self.config['min_samples_per_segment']:
                segments.append({
                    'segment_id': i + 1,
                    'data': segment,
                    'start_time': segment['datetime'].min(),
                    'end_time': segment['datetime'].max(),
                    'sample_count': len(segment)
                })
        
        print(f"  📊 有效时间段数: {len(segments)}")
        
        # 计算每个时间段的统计特征
        segment_stats = []
        for segment in segments:
            stats_dict = {}
            segment_data = segment['data']
            
            for col in feature_cols:
                if col in segment_data.columns:
                    col_data = segment_data[col].dropna()
                    if len(col_data) > 0:
                        stats_dict[col] = {
                            'mean': float(col_data.mean()),
                            'std': float(col_data.std()),
                            'median': float(col_data.median()),
                            'skewness': float(stats.skew(col_data)),
                            'kurtosis': float(stats.kurtosis(col_data)),
                            'min': float(col_data.min()),
                            'max': float(col_data.max()),
                            'q25': float(col_data.quantile(0.25)),
                            'q75': float(col_data.quantile(0.75))
                        }
            
            segment_stats.append({
                'segment_id': segment['segment_id'],
                'start_time': segment['start_time'].isoformat(),
                'end_time': segment['end_time'].isoformat(),
                'sample_count': segment['sample_count'],
                'stats': stats_dict
            })
        
        # 计算稳定性指标
        stability_scores = {}
        unstable_features = []
        
        for col in feature_cols:
            means = []
            stds = []
            medians = []
            
            for segment_stat in segment_stats:
                if col in segment_stat['stats']:
                    means.append(segment_stat['stats'][col]['mean'])
                    stds.append(segment_stat['stats'][col]['std'])
                    medians.append(segment_stat['stats'][col]['median'])
            
            if len(means) >= 2:
                # 计算均值的变异系数
                mean_cv = np.std(means) / (np.mean(np.abs(means)) + 1e-8)
                std_cv = np.std(stds) / (np.mean(stds) + 1e-8)
                median_cv = np.std(medians) / (np.mean(np.abs(medians)) + 1e-8)
                
                # 综合稳定性评分
                stability_score = 1.0 / (1.0 + mean_cv + std_cv + median_cv)
                stability_scores[col] = {
                    'stability_score': float(stability_score),
                    'mean_cv': float(mean_cv),
                    'std_cv': float(std_cv),
                    'median_cv': float(median_cv),
                    'is_stable': stability_score > (1.0 - self.config['stability_threshold'])
                }
                
                if not stability_scores[col]['is_stable']:
                    unstable_features.append(col)
        
        print(f"  📊 稳定特征数: {len(stability_scores) - len(unstable_features)}")
        print(f"  ⚠️ 不稳定特征数: {len(unstable_features)}")
        
        return {
            'segment_stats': segment_stats,
            'stability_scores': stability_scores,
            'unstable_features': unstable_features,
            'analysis_config': {
                'temporal_segments': len(segments),
                'features_analyzed': len(feature_cols),
                'stability_threshold': self.config['stability_threshold']
            }
        }
    
    def year_over_year_analysis(self, df):
        """年度对比分析"""
        print("📅 执行年度对比分析...")
        
        # 分离2024和2025年数据
        df_2024 = df[df['year'] == 2024]
        df_2025 = df[df['year'] == 2025]
        
        if len(df_2024) == 0 or len(df_2025) == 0:
            return {'error': '缺少年度数据进行对比'}
        
        print(f"  📊 2024年数据: {len(df_2024)} 行")
        print(f"  📊 2025年数据: {len(df_2025)} 行")
        
        # 获取数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date', 'year']]
        
        # 限制分析特征数量
        if len(feature_cols) > self.config['max_features_for_analysis']:
            feature_vars = df[feature_cols].var().sort_values(ascending=False)
            feature_cols = feature_vars.head(self.config['max_features_for_analysis']).index.tolist()
        
        # 年度对比统计
        year_comparison = {}
        significant_changes = []
        
        for col in feature_cols:
            if col in df_2024.columns and col in df_2025.columns:
                data_2024 = df_2024[col].dropna()
                data_2025 = df_2025[col].dropna()
                
                if len(data_2024) > 0 and len(data_2025) > 0:
                    # 基本统计对比
                    stats_2024 = {
                        'mean': float(data_2024.mean()),
                        'std': float(data_2024.std()),
                        'median': float(data_2024.median())
                    }
                    
                    stats_2025 = {
                        'mean': float(data_2025.mean()),
                        'std': float(data_2025.std()),
                        'median': float(data_2025.median())
                    }
                    
                    # 计算变化率
                    mean_change = (stats_2025['mean'] - stats_2024['mean']) / (abs(stats_2024['mean']) + 1e-8)
                    std_change = (stats_2025['std'] - stats_2024['std']) / (abs(stats_2024['std']) + 1e-8)
                    
                    # 统计显著性检验
                    try:
                        # 使用Welch's t-test
                        t_stat, p_value = stats.ttest_ind(data_2024, data_2025, equal_var=False)
                        is_significant = p_value < self.config['statistical_significance']
                    except:
                        t_stat, p_value, is_significant = 0.0, 1.0, False
                    
                    year_comparison[col] = {
                        'stats_2024': stats_2024,
                        'stats_2025': stats_2025,
                        'mean_change_rate': float(mean_change),
                        'std_change_rate': float(std_change),
                        't_statistic': float(t_stat),
                        'p_value': float(p_value),
                        'is_significant': is_significant,
                        'sample_size_2024': len(data_2024),
                        'sample_size_2025': len(data_2025)
                    }
                    
                    # 记录显著变化的特征
                    if is_significant and abs(mean_change) > 0.1:  # 10%以上的变化
                        significant_changes.append({
                            'feature': col,
                            'mean_change_rate': float(mean_change),
                            'p_value': float(p_value),
                            'change_direction': 'increase' if mean_change > 0 else 'decrease'
                        })
        
        # 按变化幅度排序
        significant_changes.sort(key=lambda x: abs(x['mean_change_rate']), reverse=True)
        
        print(f"  📊 对比特征数: {len(year_comparison)}")
        print(f"  ⚠️ 显著变化特征数: {len(significant_changes)}")
        
        return {
            'year_comparison': year_comparison,
            'significant_changes': significant_changes,
            'summary': {
                'total_features_compared': len(year_comparison),
                'significant_changes_count': len(significant_changes),
                'significance_threshold': self.config['statistical_significance']
            }
        }

    def file_type_consistency_analysis(self, df):
        """文件类型一致性分析"""
        print("📁 执行文件类型一致性分析...")

        file_types = df['file_type'].unique()
        print(f"  📊 文件类型数: {len(file_types)}")

        # 获取数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date', 'year']]

        # 限制分析特征数量
        if len(feature_cols) > 50:  # 减少特征数量以提高效率
            feature_vars = df[feature_cols].var().sort_values(ascending=False)
            feature_cols = feature_vars.head(50).index.tolist()

        # 文件类型间对比
        file_type_comparison = {}
        inconsistent_features = []

        for col in feature_cols:
            file_type_stats = {}

            for file_type in file_types:
                file_data = df[df['file_type'] == file_type][col].dropna()
                if len(file_data) > 0:
                    file_type_stats[file_type] = {
                        'mean': float(file_data.mean()),
                        'std': float(file_data.std()),
                        'median': float(file_data.median()),
                        'sample_count': len(file_data)
                    }

            if len(file_type_stats) > 1:
                # 计算文件类型间的一致性
                means = [stats['mean'] for stats in file_type_stats.values()]
                stds = [stats['std'] for stats in file_type_stats.values()]

                mean_cv = np.std(means) / (np.mean(np.abs(means)) + 1e-8)
                std_cv = np.std(stds) / (np.mean(stds) + 1e-8)

                consistency_score = 1.0 / (1.0 + mean_cv + std_cv)
                is_consistent = consistency_score > 0.8  # 一致性阈值

                file_type_comparison[col] = {
                    'file_type_stats': file_type_stats,
                    'consistency_score': float(consistency_score),
                    'mean_cv': float(mean_cv),
                    'std_cv': float(std_cv),
                    'is_consistent': is_consistent
                }

                if not is_consistent:
                    inconsistent_features.append(col)

        print(f"  📊 一致特征数: {len(file_type_comparison) - len(inconsistent_features)}")
        print(f"  ⚠️ 不一致特征数: {len(inconsistent_features)}")

        return {
            'file_type_comparison': file_type_comparison,
            'inconsistent_features': inconsistent_features,
            'summary': {
                'total_features_analyzed': len(file_type_comparison),
                'inconsistent_features_count': len(inconsistent_features),
                'file_types_analyzed': list(file_types)
            }
        }

    def run_comprehensive_analysis(self):
        """运行全面的稳定性和时序一致性分析"""
        print("🚀 开始稳定性和时序一致性分析...")

        # 加载数据
        df = self.load_temporal_data()

        # 时序稳定性分析
        stability_results = self.temporal_stability_analysis(df)

        # 年度对比分析
        year_comparison_results = self.year_over_year_analysis(df)

        # 文件类型一致性分析
        file_consistency_results = self.file_type_consistency_analysis(df)

        # 整合结果
        comprehensive_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'data_info': {
                'total_samples': len(df),
                'date_range': {
                    'start': df['datetime'].min().isoformat(),
                    'end': df['datetime'].max().isoformat()
                },
                'years_analyzed': sorted(df['year'].unique().tolist()),
                'file_types_analyzed': sorted(df['file_type'].unique().tolist())
            },
            'temporal_stability': stability_results,
            'year_over_year': year_comparison_results,
            'file_type_consistency': file_consistency_results
        }

        self.results = comprehensive_results
        return comprehensive_results

    def generate_detailed_report(self, results):
        """生成详细分析报告"""
        report = f"""
# AG特征稳定性和时序一致性分析报告

## 分析概览

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据目录**: {self.data_dir}
**总样本数**: {results['data_info']['total_samples']:,}
**时间范围**: {results['data_info']['date_range']['start']} - {results['data_info']['date_range']['end']}
**分析年份**: {', '.join(map(str, results['data_info']['years_analyzed']))}
**文件类型**: {', '.join(results['data_info']['file_types_analyzed'])}

## 时序稳定性分析

### 分析配置
- **时间段数**: {results['temporal_stability']['analysis_config']['temporal_segments']}
- **分析特征数**: {results['temporal_stability']['analysis_config']['features_analyzed']}
- **稳定性阈值**: {results['temporal_stability']['analysis_config']['stability_threshold']}

### 稳定性结果
- **稳定特征数**: {len(results['temporal_stability']['stability_scores']) - len(results['temporal_stability']['unstable_features'])}
- **不稳定特征数**: {len(results['temporal_stability']['unstable_features'])}
- **稳定性比例**: {(len(results['temporal_stability']['stability_scores']) - len(results['temporal_stability']['unstable_features'])) / len(results['temporal_stability']['stability_scores']) * 100:.1f}%

### 最不稳定特征 (Top 10)
"""

        # 获取最不稳定的特征
        stability_scores = results['temporal_stability']['stability_scores']
        unstable_sorted = sorted(
            [(k, v) for k, v in stability_scores.items() if not v['is_stable']],
            key=lambda x: x[1]['stability_score']
        )[:10]

        for i, (feature, score_info) in enumerate(unstable_sorted, 1):
            report += f"{i}. **{feature}**: 稳定性评分 = {score_info['stability_score']:.3f}\n"

        # 年度对比分析
        if 'error' not in results['year_over_year']:
            yoy_results = results['year_over_year']
            report += f"""

## 年度对比分析 (2024 vs 2025)

### 对比统计
- **对比特征数**: {yoy_results['summary']['total_features_compared']}
- **显著变化特征数**: {yoy_results['summary']['significant_changes_count']}
- **显著性阈值**: {yoy_results['summary']['significance_threshold']}

### 显著变化特征 (Top 15)
"""

            for i, change in enumerate(yoy_results['significant_changes'][:15], 1):
                direction = "📈" if change['change_direction'] == 'increase' else "📉"
                report += f"{i}. {direction} **{change['feature']}**: {change['mean_change_rate']:+.1%} (p={change['p_value']:.3f})\n"

        # 文件类型一致性分析
        if 'error' not in results['file_type_consistency']:
            consistency_results = results['file_type_consistency']
            report += f"""

## 文件类型一致性分析

### 一致性统计
- **分析特征数**: {consistency_results['summary']['total_features_analyzed']}
- **不一致特征数**: {consistency_results['summary']['inconsistent_features_count']}
- **一致性比例**: {(consistency_results['summary']['total_features_analyzed'] - consistency_results['summary']['inconsistent_features_count']) / consistency_results['summary']['total_features_analyzed'] * 100:.1f}%

### 不一致特征列表
"""

            for i, feature in enumerate(consistency_results['inconsistent_features'][:20], 1):
                consistency_score = consistency_results['file_type_comparison'][feature]['consistency_score']
                report += f"{i}. **{feature}**: 一致性评分 = {consistency_score:.3f}\n"

        report += f"""

## 分析结论和建议

### 特征稳定性评估
1. **高稳定性特征**: 建议优先使用时序稳定的特征进行模型训练
2. **不稳定特征**: 需要进一步分析不稳定的原因，可能需要特征工程处理
3. **时序一致性**: 大部分特征在不同时间段保持相对稳定

### 年度变化分析
"""

        if 'error' not in results['year_over_year']:
            significant_count = results['year_over_year']['summary']['significant_changes_count']
            total_count = results['year_over_year']['summary']['total_features_compared']

            if significant_count > 0:
                report += f"1. **显著变化**: {significant_count}/{total_count} 个特征在年度间存在显著变化\n"
                report += f"2. **模型更新**: 建议定期重新训练模型以适应特征分布变化\n"
                report += f"3. **特征监控**: 建议对显著变化的特征进行持续监控\n"
            else:
                report += f"1. **稳定性良好**: 特征在年度间保持相对稳定\n"
                report += f"2. **模型可靠性**: 模型在不同年份数据上应有较好的泛化能力\n"

        report += f"""

### 文件类型一致性评估
"""

        if 'error' not in results['file_type_consistency']:
            inconsistent_count = results['file_type_consistency']['summary']['inconsistent_features_count']
            total_count = results['file_type_consistency']['summary']['total_features_analyzed']

            if inconsistent_count > 0:
                report += f"1. **一致性问题**: {inconsistent_count}/{total_count} 个特征在不同文件类型间存在不一致\n"
                report += f"2. **数据质量**: 建议检查数据处理流程的一致性\n"
                report += f"3. **特征标准化**: 考虑对不一致特征进行标准化处理\n"
            else:
                report += f"1. **一致性良好**: 特征在不同文件类型间保持一致\n"
                report += f"2. **数据质量**: 数据处理流程具有良好的一致性\n"

        report += f"""

## 技术说明

### 分析方法
1. **时序稳定性**: 使用变异系数评估特征在不同时间段的稳定性
2. **年度对比**: 使用Welch's t-test检验年度间的显著性差异
3. **一致性分析**: 比较不同文件类型间特征分布的一致性

### 评估指标
- **稳定性评分**: 基于均值、标准差、中位数的变异系数计算
- **显著性检验**: p < {self.config['statistical_significance']} 为显著变化
- **一致性评分**: 基于文件类型间统计差异计算

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report

    def save_results(self, results, output_prefix='ag_stability_analysis'):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果
        full_results_file = os.path.join(self.output_dir, f'{output_prefix}_full_{timestamp}.json')
        with open(full_results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 完整结果已保存: {full_results_file}")

        # 保存不稳定特征列表
        if 'temporal_stability' in results:
            unstable_features_file = os.path.join(self.output_dir, f'{output_prefix}_unstable_features_{timestamp}.json')

            # 转换numpy类型为Python原生类型
            def convert_numpy_types(obj):
                if isinstance(obj, dict):
                    return {k: convert_numpy_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(v) for v in obj]
                elif hasattr(obj, 'item'):  # numpy scalar
                    return obj.item()
                elif isinstance(obj, (np.bool_, bool)):
                    return bool(obj)
                elif isinstance(obj, (np.integer, int)):
                    return int(obj)
                elif isinstance(obj, (np.floating, float)):
                    return float(obj)
                else:
                    return obj

            unstable_data = {
                'unstable_features': results['temporal_stability']['unstable_features'],
                'stability_scores': {
                    k: convert_numpy_types(v) for k, v in results['temporal_stability']['stability_scores'].items()
                    if not v['is_stable']
                }
            }
            with open(unstable_features_file, 'w', encoding='utf-8') as f:
                json.dump(unstable_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 不稳定特征列表已保存: {unstable_features_file}")

        # 保存年度变化特征
        if 'year_over_year' in results and 'significant_changes' in results['year_over_year']:
            year_changes_file = os.path.join(self.output_dir, f'{output_prefix}_year_changes_{timestamp}.csv')
            changes_df = pd.DataFrame(results['year_over_year']['significant_changes'])
            changes_df.to_csv(year_changes_file, index=False, encoding='utf-8-sig')
            print(f"✅ 年度变化特征已保存: {year_changes_file}")

        # 保存文件类型不一致特征
        if 'file_type_consistency' in results and 'inconsistent_features' in results['file_type_consistency']:
            inconsistent_file = os.path.join(self.output_dir, f'{output_prefix}_inconsistent_features_{timestamp}.json')

            # 转换numpy类型为Python原生类型的函数
            def convert_numpy_types(obj):
                if isinstance(obj, dict):
                    return {k: convert_numpy_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(v) for v in obj]
                elif hasattr(obj, 'item'):  # numpy scalar
                    return obj.item()
                elif isinstance(obj, (np.bool_, bool)):
                    return bool(obj)
                elif isinstance(obj, (np.integer, int)):
                    return int(obj)
                elif isinstance(obj, (np.floating, float)):
                    return float(obj)
                else:
                    return obj

            inconsistent_data = {
                'inconsistent_features': results['file_type_consistency']['inconsistent_features'],
                'consistency_scores': {
                    k: convert_numpy_types(v) for k, v in results['file_type_consistency']['file_type_comparison'].items()
                    if not v['is_consistent']
                }
            }
            with open(inconsistent_file, 'w', encoding='utf-8') as f:
                json.dump(inconsistent_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 不一致特征列表已保存: {inconsistent_file}")

        return {
            'full_results': full_results_file,
            'unstable_features': unstable_features_file if 'temporal_stability' in results else None,
            'year_changes': year_changes_file if 'year_over_year' in results and 'significant_changes' in results['year_over_year'] else None,
            'inconsistent_features': inconsistent_file if 'file_type_consistency' in results else None
        }


def main():
    """主函数"""
    print("📈 AG特征稳定性和时序一致性分析工具")
    print("=" * 60)

    # 创建分析器
    analyzer = StabilityTemporalAnalyzer(
        data_dir='f:/featdata/ag/',
        output_dir='./stability_analysis_results/'
    )

    try:
        # 运行全面分析
        results = analyzer.run_comprehensive_analysis()

        # 保存结果
        saved_files = analyzer.save_results(results)

        # 生成详细报告
        detailed_report = analyzer.generate_detailed_report(results)

        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(analyzer.output_dir, f'ag_stability_analysis_report_{timestamp}.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(detailed_report)

        print(f"\n🎉 分析完成!")
        print(f"📊 详细报告: {report_file}")
        print(f"📈 完整结果: {saved_files['full_results']}")
        if saved_files['unstable_features']:
            print(f"⚠️ 不稳定特征: {saved_files['unstable_features']}")
        if saved_files['year_changes']:
            print(f"📅 年度变化: {saved_files['year_changes']}")
        if saved_files['inconsistent_features']:
            print(f"📁 不一致特征: {saved_files['inconsistent_features']}")

        # 打印摘要
        print(f"\n📋 分析摘要:")
        print(f"  总样本数: {results['data_info']['total_samples']:,}")

        if 'temporal_stability' in results:
            stability_results = results['temporal_stability']
            total_features = len(stability_results['stability_scores'])
            unstable_count = len(stability_results['unstable_features'])
            print(f"  稳定特征: {total_features - unstable_count}/{total_features}")

        if 'year_over_year' in results and 'summary' in results['year_over_year']:
            yoy_summary = results['year_over_year']['summary']
            print(f"  年度显著变化: {yoy_summary['significant_changes_count']}/{yoy_summary['total_features_compared']}")

        if 'file_type_consistency' in results and 'summary' in results['file_type_consistency']:
            consistency_summary = results['file_type_consistency']['summary']
            inconsistent_count = consistency_summary['inconsistent_features_count']
            total_count = consistency_summary['total_features_analyzed']
            print(f"  文件类型一致: {total_count - inconsistent_count}/{total_count}")

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
