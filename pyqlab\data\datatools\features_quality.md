### 模型训练时，使用huber较mse优化时有比较好的收敛

### 为什么你的模型会出现这种情况？

**你的情况完美地展示了这两种损失函数的特性：**

* **使用MSE不收敛**：这强烈暗示你的训练集中存在一些“坏”数据点（离群点）。这些点的预测误差非常大，导致MSE计算出巨大的损失和梯度，使得优化器（如Adam、SGD）在更新权重时“用力过猛”，破坏了整个训练过程。
* **使用Huber可以收敛**：当你换用Huber Loss后，它智能地将那些由离群点产生的大误差“降级处理”，使其梯度保持在一个可控的范围内。这样，即使有离群点存在，它们也无法主导整个训练过程。模型可以忽略这些噪声，专注于从大多数“好”的样本中学习规律，因此能够稳定收敛。

### 建议的行动方案

**既然你已经通过更换损失函数解决了收敛问题，这本身就是一个很好的解决方案。除此之外，你还可以：**

* **数据分析与预处理**：

  * **可视化你的数据**：画出你的目标变量**y**的分布图（如箱线图、直方图），或者画出特征与目标变量的散点图，直观地寻找离群点。
* **检查高误差样本**：在用MSE训练失败后，可以检查一下哪些样本产生了最大的误差，看看它们是不是数据录入错误、测量异常或其他问题。
* **处理离群点**：根据分析结果，你可以选择删除这些离群点（如果确定是错误数据），或者对它们进行转换（如取对数**log**）来减小其影响。
