# TCN模型训练脚本 - PowerShell版本
# 使用参数化配置提高灵活性

param(
    [string]$Version = "HTCN-SF",
    [string]$DataPath = "f:/featdata/sf",
    [string]$FutCodes = "SF_FUT_CODES",
    [string]$BlockName = "sf",
    [string]$Years = "[2025]",
    [string]$StartDate = "2025-01-01",
    [string]$EndDate = "2025-07-31",
    [string]$FdSet = "[(1,1), (2,1)]",
    [int]$NumPeriods = 2,
    [int]$MainPeriodIdx = 0,
    [bool]$FilterExtreme = $false,
    [double]$ExtremeThreshold = 5.0,
    [int]$SeqLen = 20,
    [string]$Direct = "ls",
    [int]$BatchSize = 64,
    [int]$KFolds = 5,
    [string]$TcnChannels = "[128, 256]",
    [int]$TcnOutputDim = 256,
    [string]$SelFdNames = "SEL_FACTOR_NAMES",
    [int]$FeatDim = 52,
    [int]$CodeEmbeddingDim = 4,
    [int]$KernelSize = 3,
    [string]$ModelName = "hierarchical_tcn",
    [string]$ModelPath = "pyqlab.models.fintimeseries",
    [string]$Mode = "regression",
    [string]$Loss = "huber",
    [double]$Dropout = 0.4,
    [int]$EarlyStop = 7,
    [double]$MinDelta = 0.003,
    [double]$WeightDecay = 1e-4,
    [int]$MaxEpochs = 50,
    [string]$LrScheduler = "cosine",
    [int]$LrDecaySteps = 5,
    [double]$LrDecayRate = 0.1,
    [double]$Lr = 1e-4,
    [string]$CvType = "none",
    [double]$BestScore,
    [bool]$ExportOnnx = $false,
    [bool]$Verbose = $false
)

# 预设配置
$Presets = @{
    "HTCN-AG" = @{
        Version = "HTCN-AG"
        DataPath = "f:/featdata/ag"
        FutCodes = "SF_FUT_CODES"
        BlockName = "ag"
    }
    "HTCN-SF" = @{
        Version = "HTCN-SF"
        DataPath = "f:/featdata/sf"
        FutCodes = "SF_FUT_CODES"
        BlockName = "sf"
    }
    "HTCN-TOP" = @{
        Version = "HTCN"
        DataPath = "f:/featdata/top"
        FutCodes = "MAIN_SEL_FUT_CODES"
        BlockName = "top"
    }
}

# 应用预设配置（命令行参数优先）
if ($Presets.ContainsKey($Version)) {
    $preset = $Presets[$Version]
    if ($PSBoundParameters['DataPath'] -eq $null) { $DataPath = $preset.DataPath }
    if ($PSBoundParameters['FutCodes'] -eq $null) { $FutCodes = $preset.FutCodes }
    if ($PSBoundParameters['BlockName'] -eq $null) { $BlockName = $preset.BlockName }
    # Version 本身不覆盖，保持命令行为主
}

# 显示当前配置
Write-Host "=== TCN模型训练配置 ===" -ForegroundColor Green
Write-Host "版本: $Version" -ForegroundColor Yellow
Write-Host "数据路径: $DataPath" -ForegroundColor Yellow
Write-Host "期货代码: $FutCodes" -ForegroundColor Yellow
Write-Host "训练日期: $StartDate 到 $EndDate" -ForegroundColor Yellow
Write-Host "批大小: $BatchSize, 序列长度: $SeqLen" -ForegroundColor Yellow
Write-Host "最大训练轮数: $MaxEpochs, 学习率: $Lr" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Green

# 切换到工作目录
Set-Location "e:\lab\RoboQuant\pylab"

# 构建Python命令参数
$PythonArgs = @(
    "./pyqlab/pl/train_tcn.py",
    "--version=$Version",
    "--data_path=$DataPath",
    "--fut_codes=$FutCodes",
    "--block_name=$BlockName",
    "--years=$Years",
    "--start_date=$StartDate",
    "--end_date=$EndDate",
    "--fd_set=$FdSet",
    "--num_periods=$NumPeriods",
    "--main_period_idx=$MainPeriodIdx",
    "--seq_len=$SeqLen",
    "--direct=$Direct",
    "--batch_size=$BatchSize",
    "--k_folds=$KFolds",
    "--tcn_channels=$TcnChannels",
    "--tcn_output_dim=$TcnOutputDim",
    "--sel_fd_names=$SelFdNames",
    "--feat_dim=$FeatDim",
    "--code_embedding_dim=$CodeEmbeddingDim",
    "--kernel_size=$KernelSize",
    "--model_name=$ModelName",
    "--model_path=$ModelPath",
    "--mode=$Mode",
    "--loss=$Loss",
    "--dropout=$Dropout",
    "--early_stop=$EarlyStop",
    "--min_delta=$MinDelta",
    "--weight_decay=$WeightDecay",
    "--max_epochs=$MaxEpochs",
    "--lr_scheduler=$LrScheduler",
    "--lr_decay_steps=$LrDecaySteps",
    "--lr_decay_rate=$LrDecayRate",
    "--lr=$Lr",
    "--cv_type=$CvType",
    "--is_filter_extreme=$($FilterExtreme.ToString())",
    "--export_onnx=$($ExportOnnx.ToString())",
    "--verbose=$($Verbose.ToString())"
)

# 添加可选参数
if ($FilterExtreme) {
    $PythonArgs += "--extreme_threshold=$ExtremeThreshold"
}

if ($BestScore) {
    $PythonArgs += "--best_score=$BestScore"
}

# 执行训练命令
Write-Host "开始训练..." -ForegroundColor Green
try {
    & python $PythonArgs
    Write-Host "训练完成!" -ForegroundColor Green
} catch {
    Write-Host "训练过程中出现错误: $_" -ForegroundColor Red
    exit 1
}
# 使用示例:
#
# 1. 使用默认参数运行:
# .\train_tcn.ps1
#
# 2. 修改关键参数:
# .\train_tcn.ps1 -BatchSize 128 -MaxEpochs 100 -Lr 2e-4
#
# 3. 切换到TOP配置:
# .\train_tcn.ps1 -Version "HTCN" -DataPath "f:/featdata/top" -FutCodes "MAIN_SEL_FUT_CODES" -BlockName "top"
#
# 4. 启用极值过滤和ONNX导出:
# .\train_tcn.ps1 -FilterExtreme -ExportOnnx -Verbose
#
# 5. 自定义日期范围:
# .\train_tcn.ps1 -StartDate "2025-01-01" -EndDate "2025-12-31" -Years "[2025]"
#
# 6. 调整模型架构:
# .\train_tcn.ps1 -TcnChannels "[64, 128, 256]" -TcnOutputDim 128 -SeqLen 30