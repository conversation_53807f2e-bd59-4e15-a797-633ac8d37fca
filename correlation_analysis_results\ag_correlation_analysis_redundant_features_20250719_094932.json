{"redundant_features_list": ["CMO_1", "EMA_SLOW_1", "T3_SLOW_2", "TL_FAST_2", "EMA_SLOW_2", "NEW_CHANGE_PERCENT_2", "SQUEEZE_ZERO_BARS_1", "HYO_CROSS_BARS_1", "TREND_HIGHEST_2", "TL_THRESHOLD_2", "TEMA_FAST_1", "BOLL_MID_2", "LR_SLOPE_FAST_THRESHOLD_1", "CLOSE_2", "MAMA_SLOW_2", "ROC_2", "BAND_GRADIENT_2", "ROC_1", "BAND_BK_BARS_2", "SQUEEZE_GAP_THRESHOLD_1", "AD_1", "TREND_VALUE_1", "KAMA_SLOW_1", "SQUEEZE_MDL_2", "ADXR_1", "BAR_STICK_LENGTH_2", "DEMA_FAST_1", "TL_SLOW_1", "MA_SLOW_2", "ATR_2", "TRIX_FAST_2", "TREND_BARS_1", "STDDEV_THRESHOLD_1", "MOMENTUM_1", "TEMA_SLOW_2", "DEMA_SLOW_1", "TRIMA_FAST_1", "RSI_1", "TRIMA_FAST_2", "TREND_INBARS_2", "TREND_LEVEL_1", "STDDEV_SLOW_2", "SQUEEZE_1", "BAND_GAP_1", "MA_FAST_1", "MACD_DIFF_2", "LR_SLOPE_SLOW_2", "KDJ_D_1", "HIGH_1", "T3_FAST_1", "T3_SLOW_1", "LR_SLOPE_MIDD_2", "LR_SLOPE_MIDD_1", "KDJ_K_1", "TRIMA_SLOW_1", "NEW_1", "SQUEEZE_MDL_1", "DEMA_SLOW_2", "TL_FAST_1", "MOM_1", "MACD_DIFF_1", "MACD_DEA_2", "T3_FAST_2", "MOMENTUM_2", "KAMA_FAST_2", "ATR_1", "BAND_UPL_1", "BAND_GRADIENT_1", "TSF_1", "SQUEEZE_KC_UPL_2", "BAND_GAP_2", "TRIMA_SLOW_2", "MOM_2", "RSI_2", "MACD_DEA_1", "TRIX_SLOW_2", "BOLL_UP_2", "MAMA_SLOW_1", "MOMENTUM_THRESHOLD_1", "DX_2", "TSF_2", "LR_SLOPE_SLOW_THRESHOLD_2", "MAMA_FAST_2", "BAND_EXPAND_1", "SQUEEZE_KC_DWL_2", "CLOSE_1", "BAND_GRADIENT_THRESHOLD_2", "ADX_1", "MA_SLOW_1", "TEMA_SLOW_1", "BOLL_MID_1", "MAMA_FAST_1", "SAR_1", "APO_2", "KAMA_FAST_1", "NEW_CHANGE_PERCENT_1", "BAND_MDL_1", "LR_SLOPE_SLOW_1", "BAND_MDL_2", "TREND_LOWEST_2", "BAND_WIDTH_2", "SQUEEZE_SIGNAL_1", "EMA_FAST_1", "EMA_FAST_2", "TREND_INPOSR_1", "TEMA_FAST_2", "TREND_HLR_2", "MA_FAST_2", "NEW_2"], "redundant_groups": [["BAND_POSITION_1", "CMO_1", "RSI_1"], ["AD_2", "AD_1"], ["BAND_BK_BARS_1", "BAND_BK_BARS_2"], ["BAND_EXPAND_2", "BAND_EXPAND_1"], ["CMO_2", "LR_SLOPE_MIDD_2", "TRIX_FAST_2", "RSI_2", "APO_2", "BAND_GAP_1", "BAND_GAP_2"], ["BAND_GRADIENT_THRESHOLD_1", "BAND_GRADIENT_THRESHOLD_2"], ["BAND_WIDTH_1", "BAND_WIDTH_2"], ["BAR_STICK_LENGTH_1", "BAR_STICK_LENGTH_2"], ["DEMA_FAST_2", "SQUEEZE_KC_DWL_2", "MACD_DIFF_1", "TEMA_SLOW_2", "CLOSE_1", "MACD_DEA_2", "T3_FAST_2", "EMA_SLOW_1", "T3_SLOW_2", "DEMA_SLOW_1", "TRIMA_FAST_1", "MA_SLOW_1", "TEMA_SLOW_1", "BOLL_MID_1", "MAMA_FAST_1", "EMA_SLOW_2", "TRIMA_FAST_2", "NEW_CHANGE_PERCENT_2", "KAMA_FAST_2", "TSF_1", "KAMA_FAST_1", "NEW_CHANGE_PERCENT_1", "BAND_MDL_1", "SQUEEZE_KC_UPL_2", "TEMA_FAST_1", "TRIMA_SLOW_2", "MA_FAST_1", "MACD_DIFF_2", "BOLL_MID_2", "MACD_DEA_1", "BAND_MDL_2", "CLOSE_2", "MAMA_SLOW_2", "EMA_FAST_1", "EMA_FAST_2", "TEMA_FAST_2", "T3_FAST_1", "MAMA_SLOW_1", "T3_SLOW_1", "MA_FAST_2", "KAMA_SLOW_1", "SQUEEZE_MDL_2", "NEW_2", "TSF_2", "TRIMA_SLOW_1", "DEMA_FAST_1", "NEW_1", "MAMA_FAST_2", "SQUEEZE_MDL_1", "MA_SLOW_2", "DEMA_SLOW_2"], ["HIGH_2", "HIGH_1"], ["HYO_CROSS_BARS_2", "HYO_CROSS_BARS_1"], ["LR_SLOPE_FAST_THRESHOLD_2", "LR_SLOPE_FAST_THRESHOLD_1"], ["LR_SLOPE_SLOW_THRESHOLD_1", "LR_SLOPE_SLOW_THRESHOLD_2"], ["MOMENTUM_THRESHOLD_2", "MOMENTUM_THRESHOLD_1"], ["SQUEEZE_2", "SQUEEZE_1"], ["SQUEEZE_GAP_THRESHOLD_2", "SQUEEZE_GAP_THRESHOLD_1"], ["SQUEEZE_SIGNAL_2", "SQUEEZE_SIGNAL_1"], ["SQUEEZE_ZERO_BARS_2", "SQUEEZE_ZERO_BARS_1"], ["STDDEV_THRESHOLD_2", "STDDEV_THRESHOLD_1"], ["TL_THRESHOLD_1", "TL_THRESHOLD_2"], ["TREND_BARS_2", "TREND_BARS_1"], ["TREND_HIGHEST_1", "TREND_HIGHEST_2"], ["TREND_HLR_1", "TREND_HLR_2"], ["TREND_INBARS_1", "TREND_INBARS_2"], ["TREND_INPOSR_2", "TREND_INPOSR_1"], ["TREND_LEVEL_2", "TREND_LEVEL_1"], ["TREND_LOWEST_1", "TREND_LOWEST_2"], ["TREND_VALUE_2", "TREND_VALUE_1"], ["MOMENTUM_SLOW_1", "ROC_1", "MOM_1"], ["MOMENTUM_SLOW_2", "ROC_2", "MOM_2"], ["NATR_2", "ATR_2"], ["NATR_1", "ATR_1"], ["TL_SLOW_2", "LR_SLOPE_SLOW_1", "TL_FAST_1", "MOMENTUM_1", "LR_SLOPE_SLOW_2", "BAND_GRADIENT_1", "TL_SLOW_1", "BAND_GRADIENT_2", "TL_FAST_2", "MOMENTUM_2"], ["KDJ_D_2", "KDJ_D_1"], ["ADX_2", "ADX_1"], ["SAR_2", "SAR_1"], ["KDJ_K_2", "KDJ_K_1"], ["DX_1", "DX_2"], ["TRIX_FAST_1", "LR_SLOPE_MIDD_1"], ["BAND_UPL_2", "BAND_UPL_1"], ["TRIX_SLOW_1", "TRIX_SLOW_2"], ["BOLL_UP_1", "BOLL_UP_2"], ["SQUEEZE_GAP_2", "STDDEV_SLOW_2"], ["ADXR_2", "ADXR_1"]], "recommendations": [{"group": ["BAND_POSITION_1", "CMO_1", "RSI_1"], "keep": "BAND_POSITION_1", "remove": ["CMO_1", "RSI_1"], "group_size": 3}, {"group": ["AD_2", "AD_1"], "keep": "AD_2", "remove": ["AD_1"], "group_size": 2}, {"group": ["BAND_BK_BARS_1", "BAND_BK_BARS_2"], "keep": "BAND_BK_BARS_1", "remove": ["BAND_BK_BARS_2"], "group_size": 2}, {"group": ["BAND_EXPAND_2", "BAND_EXPAND_1"], "keep": "BAND_EXPAND_2", "remove": ["BAND_EXPAND_1"], "group_size": 2}, {"group": ["CMO_2", "LR_SLOPE_MIDD_2", "TRIX_FAST_2", "RSI_2", "APO_2", "BAND_GAP_1", "BAND_GAP_2"], "keep": "CMO_2", "remove": ["LR_SLOPE_MIDD_2", "TRIX_FAST_2", "RSI_2", "APO_2", "BAND_GAP_1", "BAND_GAP_2"], "group_size": 7}, {"group": ["BAND_GRADIENT_THRESHOLD_1", "BAND_GRADIENT_THRESHOLD_2"], "keep": "BAND_GRADIENT_THRESHOLD_1", "remove": ["BAND_GRADIENT_THRESHOLD_2"], "group_size": 2}, {"group": ["BAND_WIDTH_1", "BAND_WIDTH_2"], "keep": "BAND_WIDTH_1", "remove": ["BAND_WIDTH_2"], "group_size": 2}, {"group": ["BAR_STICK_LENGTH_1", "BAR_STICK_LENGTH_2"], "keep": "BAR_STICK_LENGTH_1", "remove": ["BAR_STICK_LENGTH_2"], "group_size": 2}, {"group": ["DEMA_FAST_2", "SQUEEZE_KC_DWL_2", "MACD_DIFF_1", "TEMA_SLOW_2", "CLOSE_1", "MACD_DEA_2", "T3_FAST_2", "EMA_SLOW_1", "T3_SLOW_2", "DEMA_SLOW_1", "TRIMA_FAST_1", "MA_SLOW_1", "TEMA_SLOW_1", "BOLL_MID_1", "MAMA_FAST_1", "EMA_SLOW_2", "TRIMA_FAST_2", "NEW_CHANGE_PERCENT_2", "KAMA_FAST_2", "TSF_1", "KAMA_FAST_1", "NEW_CHANGE_PERCENT_1", "BAND_MDL_1", "SQUEEZE_KC_UPL_2", "TEMA_FAST_1", "TRIMA_SLOW_2", "MA_FAST_1", "MACD_DIFF_2", "BOLL_MID_2", "MACD_DEA_1", "BAND_MDL_2", "CLOSE_2", "MAMA_SLOW_2", "EMA_FAST_1", "EMA_FAST_2", "TEMA_FAST_2", "T3_FAST_1", "MAMA_SLOW_1", "T3_SLOW_1", "MA_FAST_2", "KAMA_SLOW_1", "SQUEEZE_MDL_2", "NEW_2", "TSF_2", "TRIMA_SLOW_1", "DEMA_FAST_1", "NEW_1", "MAMA_FAST_2", "SQUEEZE_MDL_1", "MA_SLOW_2", "DEMA_SLOW_2"], "keep": "DEMA_FAST_2", "remove": ["SQUEEZE_KC_DWL_2", "MACD_DIFF_1", "TEMA_SLOW_2", "CLOSE_1", "MACD_DEA_2", "T3_FAST_2", "EMA_SLOW_1", "T3_SLOW_2", "DEMA_SLOW_1", "TRIMA_FAST_1", "MA_SLOW_1", "TEMA_SLOW_1", "BOLL_MID_1", "MAMA_FAST_1", "EMA_SLOW_2", "TRIMA_FAST_2", "NEW_CHANGE_PERCENT_2", "KAMA_FAST_2", "TSF_1", "KAMA_FAST_1", "NEW_CHANGE_PERCENT_1", "BAND_MDL_1", "SQUEEZE_KC_UPL_2", "TEMA_FAST_1", "TRIMA_SLOW_2", "MA_FAST_1", "MACD_DIFF_2", "BOLL_MID_2", "MACD_DEA_1", "BAND_MDL_2", "CLOSE_2", "MAMA_SLOW_2", "EMA_FAST_1", "EMA_FAST_2", "TEMA_FAST_2", "T3_FAST_1", "MAMA_SLOW_1", "T3_SLOW_1", "MA_FAST_2", "KAMA_SLOW_1", "SQUEEZE_MDL_2", "NEW_2", "TSF_2", "TRIMA_SLOW_1", "DEMA_FAST_1", "NEW_1", "MAMA_FAST_2", "SQUEEZE_MDL_1", "MA_SLOW_2", "DEMA_SLOW_2"], "group_size": 51}, {"group": ["HIGH_2", "HIGH_1"], "keep": "HIGH_2", "remove": ["HIGH_1"], "group_size": 2}, {"group": ["HYO_CROSS_BARS_2", "HYO_CROSS_BARS_1"], "keep": "HYO_CROSS_BARS_2", "remove": ["HYO_CROSS_BARS_1"], "group_size": 2}, {"group": ["LR_SLOPE_FAST_THRESHOLD_2", "LR_SLOPE_FAST_THRESHOLD_1"], "keep": "LR_SLOPE_FAST_THRESHOLD_2", "remove": ["LR_SLOPE_FAST_THRESHOLD_1"], "group_size": 2}, {"group": ["LR_SLOPE_SLOW_THRESHOLD_1", "LR_SLOPE_SLOW_THRESHOLD_2"], "keep": "LR_SLOPE_SLOW_THRESHOLD_1", "remove": ["LR_SLOPE_SLOW_THRESHOLD_2"], "group_size": 2}, {"group": ["MOMENTUM_THRESHOLD_2", "MOMENTUM_THRESHOLD_1"], "keep": "MOMENTUM_THRESHOLD_2", "remove": ["MOMENTUM_THRESHOLD_1"], "group_size": 2}, {"group": ["SQUEEZE_2", "SQUEEZE_1"], "keep": "SQUEEZE_2", "remove": ["SQUEEZE_1"], "group_size": 2}, {"group": ["SQUEEZE_GAP_THRESHOLD_2", "SQUEEZE_GAP_THRESHOLD_1"], "keep": "SQUEEZE_GAP_THRESHOLD_2", "remove": ["SQUEEZE_GAP_THRESHOLD_1"], "group_size": 2}, {"group": ["SQUEEZE_SIGNAL_2", "SQUEEZE_SIGNAL_1"], "keep": "SQUEEZE_SIGNAL_2", "remove": ["SQUEEZE_SIGNAL_1"], "group_size": 2}, {"group": ["SQUEEZE_ZERO_BARS_2", "SQUEEZE_ZERO_BARS_1"], "keep": "SQUEEZE_ZERO_BARS_2", "remove": ["SQUEEZE_ZERO_BARS_1"], "group_size": 2}, {"group": ["STDDEV_THRESHOLD_2", "STDDEV_THRESHOLD_1"], "keep": "STDDEV_THRESHOLD_2", "remove": ["STDDEV_THRESHOLD_1"], "group_size": 2}, {"group": ["TL_THRESHOLD_1", "TL_THRESHOLD_2"], "keep": "TL_THRESHOLD_1", "remove": ["TL_THRESHOLD_2"], "group_size": 2}, {"group": ["TREND_BARS_2", "TREND_BARS_1"], "keep": "TREND_BARS_2", "remove": ["TREND_BARS_1"], "group_size": 2}, {"group": ["TREND_HIGHEST_1", "TREND_HIGHEST_2"], "keep": "TREND_HIGHEST_1", "remove": ["TREND_HIGHEST_2"], "group_size": 2}, {"group": ["TREND_HLR_1", "TREND_HLR_2"], "keep": "TREND_HLR_1", "remove": ["TREND_HLR_2"], "group_size": 2}, {"group": ["TREND_INBARS_1", "TREND_INBARS_2"], "keep": "TREND_INBARS_1", "remove": ["TREND_INBARS_2"], "group_size": 2}, {"group": ["TREND_INPOSR_2", "TREND_INPOSR_1"], "keep": "TREND_INPOSR_2", "remove": ["TREND_INPOSR_1"], "group_size": 2}, {"group": ["TREND_LEVEL_2", "TREND_LEVEL_1"], "keep": "TREND_LEVEL_2", "remove": ["TREND_LEVEL_1"], "group_size": 2}, {"group": ["TREND_LOWEST_1", "TREND_LOWEST_2"], "keep": "TREND_LOWEST_1", "remove": ["TREND_LOWEST_2"], "group_size": 2}, {"group": ["TREND_VALUE_2", "TREND_VALUE_1"], "keep": "TREND_VALUE_2", "remove": ["TREND_VALUE_1"], "group_size": 2}, {"group": ["MOMENTUM_SLOW_1", "ROC_1", "MOM_1"], "keep": "MOMENTUM_SLOW_1", "remove": ["ROC_1", "MOM_1"], "group_size": 3}, {"group": ["MOMENTUM_SLOW_2", "ROC_2", "MOM_2"], "keep": "MOMENTUM_SLOW_2", "remove": ["ROC_2", "MOM_2"], "group_size": 3}, {"group": ["NATR_2", "ATR_2"], "keep": "NATR_2", "remove": ["ATR_2"], "group_size": 2}, {"group": ["NATR_1", "ATR_1"], "keep": "NATR_1", "remove": ["ATR_1"], "group_size": 2}, {"group": ["TL_SLOW_2", "LR_SLOPE_SLOW_1", "TL_FAST_1", "MOMENTUM_1", "LR_SLOPE_SLOW_2", "BAND_GRADIENT_1", "TL_SLOW_1", "BAND_GRADIENT_2", "TL_FAST_2", "MOMENTUM_2"], "keep": "TL_SLOW_2", "remove": ["LR_SLOPE_SLOW_1", "TL_FAST_1", "MOMENTUM_1", "LR_SLOPE_SLOW_2", "BAND_GRADIENT_1", "TL_SLOW_1", "BAND_GRADIENT_2", "TL_FAST_2", "MOMENTUM_2"], "group_size": 10}, {"group": ["KDJ_D_2", "KDJ_D_1"], "keep": "KDJ_D_2", "remove": ["KDJ_D_1"], "group_size": 2}, {"group": ["ADX_2", "ADX_1"], "keep": "ADX_2", "remove": ["ADX_1"], "group_size": 2}, {"group": ["SAR_2", "SAR_1"], "keep": "SAR_2", "remove": ["SAR_1"], "group_size": 2}, {"group": ["KDJ_K_2", "KDJ_K_1"], "keep": "KDJ_K_2", "remove": ["KDJ_K_1"], "group_size": 2}, {"group": ["DX_1", "DX_2"], "keep": "DX_1", "remove": ["DX_2"], "group_size": 2}, {"group": ["TRIX_FAST_1", "LR_SLOPE_MIDD_1"], "keep": "TRIX_FAST_1", "remove": ["LR_SLOPE_MIDD_1"], "group_size": 2}, {"group": ["BAND_UPL_2", "BAND_UPL_1"], "keep": "BAND_UPL_2", "remove": ["BAND_UPL_1"], "group_size": 2}, {"group": ["TRIX_SLOW_1", "TRIX_SLOW_2"], "keep": "TRIX_SLOW_1", "remove": ["TRIX_SLOW_2"], "group_size": 2}, {"group": ["BOLL_UP_1", "BOLL_UP_2"], "keep": "BOLL_UP_1", "remove": ["BOLL_UP_2"], "group_size": 2}, {"group": ["SQUEEZE_GAP_2", "STDDEV_SLOW_2"], "keep": "SQUEEZE_GAP_2", "remove": ["STDDEV_SLOW_2"], "group_size": 2}, {"group": ["ADXR_2", "ADXR_1"], "keep": "ADXR_2", "remove": ["ADXR_1"], "group_size": 2}]}