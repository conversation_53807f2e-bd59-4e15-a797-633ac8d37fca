import pandas as pd
import os

def explore_ag_data():
    data_dir = 'f:/featdata/ag/'
    files = [f for f in os.listdir(data_dir) if f.endswith('.parquet')]
    
    print('=== AG数据集文件概览 ===')
    total_size = 0
    total_rows = 0
    
    for file in sorted(files):
        file_path = os.path.join(data_dir, file)
        df = pd.read_parquet(file_path)
        file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
        total_size += file_size
        total_rows += len(df)
        
        print(f'{file}:')
        print(f'  形状: {df.shape}')
        print(f'  大小: {file_size:.1f} MB')
        if 'code' in df.columns:
            print(f'  代码数: {df["code"].nunique()}')
        if 'date' in df.columns:
            date_min = pd.to_datetime(df['date'], unit='s').min()
            date_max = pd.to_datetime(df['date'], unit='s').max()
            print(f'  时间范围: {date_min} - {date_max}')
        print()
    
    print(f'总文件数: {len(files)}')
    print(f'总大小: {total_size:.1f} MB')
    print(f'总数据行数: {total_rows:,}')
    
    # 分析列结构
    print('\n=== 列结构分析 ===')
    sample_file = os.path.join(data_dir, files[0])
    df_sample = pd.read_parquet(sample_file)
    
    print(f'总列数: {len(df_sample.columns)}')
    print(f'数据类型分布:')
    print(df_sample.dtypes.value_counts())
    
    # 按特征类型分组
    feature_groups = {}
    for col in df_sample.columns:
        if col in ['code', 'date', 'change']:
            continue
        
        # 根据列名前缀分组
        if '_' in col:
            prefix = col.split('_')[0]
            if prefix not in feature_groups:
                feature_groups[prefix] = []
            feature_groups[prefix].append(col)
    
    print(f'\n特征组分析:')
    for group, cols in sorted(feature_groups.items()):
        print(f'  {group}: {len(cols)}个特征')
    
    return df_sample, files

if __name__ == '__main__':
    df_sample, files = explore_ag_data()
