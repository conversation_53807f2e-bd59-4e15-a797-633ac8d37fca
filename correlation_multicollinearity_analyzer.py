"""
特征相关性和多重共线性分析工具
专门分析AG数据中特征间的相关性，检测多重共线性问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr
from sklearn.preprocessing import StandardScaler
from statsmodels.stats.outliers_influence import variance_inflation_factor
import warnings
import json
import os
from datetime import datetime

warnings.filterwarnings('ignore')

class CorrelationMulticollinearityAnalyzer:
    """相关性和多重共线性分析器"""
    
    def __init__(self, data_dir='f:/featdata/ag/', output_dir='./correlation_analysis_results/'):
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 分析配置
        self.config = {
            'high_correlation_threshold': 0.95,    # 高相关性阈值
            'moderate_correlation_threshold': 0.7, # 中等相关性阈值
            'vif_threshold': 10.0,                 # VIF阈值
            'max_features_for_vif': 50,            # VIF分析的最大特征数
            'sample_size_for_analysis': 10000      # 分析用的样本数量
        }
        
        self.results = {}
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_sample_data(self):
        """加载样本数据进行分析"""
        print("🔄 加载样本数据...")
        
        # 选择一个代表性文件进行分析
        sample_file = None
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.parquet') and '2025' in filename:
                sample_file = os.path.join(self.data_dir, filename)
                break
        
        if not sample_file:
            # 如果没有2025年数据，选择第一个文件
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.parquet'):
                    sample_file = os.path.join(self.data_dir, filename)
                    break
        
        if not sample_file:
            raise ValueError("未找到数据文件")
        
        df = pd.read_parquet(sample_file)
        print(f"  ✅ 加载文件: {os.path.basename(sample_file)}")
        print(f"  📊 数据形状: {df.shape}")
        
        # 获取数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in ['date']]
        
        # 清理数据
        df_clean = df[feature_cols].dropna()
        
        # 如果数据量太大，进行采样
        if len(df_clean) > self.config['sample_size_for_analysis']:
            df_clean = df_clean.sample(n=self.config['sample_size_for_analysis'], random_state=42)
            print(f"  📉 采样到 {len(df_clean)} 行")
        
        print(f"  🎯 分析特征数: {len(feature_cols)}")
        return df_clean, feature_cols
    
    def correlation_analysis(self, df, feature_cols):
        """相关性分析"""
        print("🔗 执行相关性分析...")
        
        # 计算相关性矩阵
        corr_matrix = df.corr()
        
        # 找出高相关性特征对
        high_corr_pairs = []
        moderate_corr_pairs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                feature1 = corr_matrix.columns[i]
                feature2 = corr_matrix.columns[j]
                
                if abs(corr_val) >= self.config['high_correlation_threshold']:
                    high_corr_pairs.append({
                        'feature1': feature1,
                        'feature2': feature2,
                        'correlation': float(corr_val),
                        'abs_correlation': float(abs(corr_val))
                    })
                elif abs(corr_val) >= self.config['moderate_correlation_threshold']:
                    moderate_corr_pairs.append({
                        'feature1': feature1,
                        'feature2': feature2,
                        'correlation': float(corr_val),
                        'abs_correlation': float(abs(corr_val))
                    })
        
        # 按相关性排序
        high_corr_pairs.sort(key=lambda x: x['abs_correlation'], reverse=True)
        moderate_corr_pairs.sort(key=lambda x: x['abs_correlation'], reverse=True)
        
        # 统计信息
        correlation_stats = {
            'total_feature_pairs': len(corr_matrix.columns) * (len(corr_matrix.columns) - 1) // 2,
            'high_correlation_pairs': len(high_corr_pairs),
            'moderate_correlation_pairs': len(moderate_corr_pairs),
            'mean_abs_correlation': float(corr_matrix.abs().mean().mean()),
            'max_correlation': float(corr_matrix.abs().max().max()),
            'correlation_matrix_shape': corr_matrix.shape
        }
        
        print(f"  📈 高相关性特征对: {len(high_corr_pairs)}")
        print(f"  📊 中等相关性特征对: {len(moderate_corr_pairs)}")
        print(f"  📉 平均绝对相关性: {correlation_stats['mean_abs_correlation']:.3f}")
        
        return {
            'correlation_matrix': corr_matrix,
            'high_correlation_pairs': high_corr_pairs,
            'moderate_correlation_pairs': moderate_corr_pairs,
            'correlation_stats': correlation_stats
        }
    
    def identify_redundant_features(self, correlation_results):
        """识别冗余特征"""
        print("🔍 识别冗余特征...")
        
        high_corr_pairs = correlation_results['high_correlation_pairs']
        
        # 构建相关性图
        feature_connections = {}
        for pair in high_corr_pairs:
            f1, f2 = pair['feature1'], pair['feature2']
            
            if f1 not in feature_connections:
                feature_connections[f1] = []
            if f2 not in feature_connections:
                feature_connections[f2] = []
            
            feature_connections[f1].append((f2, pair['abs_correlation']))
            feature_connections[f2].append((f1, pair['abs_correlation']))
        
        # 识别冗余特征组
        redundant_groups = []
        processed_features = set()
        
        for feature, connections in feature_connections.items():
            if feature in processed_features:
                continue
            
            # 找到与当前特征高度相关的所有特征
            group = {feature}
            queue = [feature]
            
            while queue:
                current = queue.pop(0)
                if current in feature_connections:
                    for connected_feature, corr in feature_connections[current]:
                        if connected_feature not in group and corr >= self.config['high_correlation_threshold']:
                            group.add(connected_feature)
                            queue.append(connected_feature)
            
            if len(group) > 1:
                redundant_groups.append(list(group))
                processed_features.update(group)
        
        # 为每个冗余组推荐保留的特征
        recommendations = []
        all_redundant_features = set()
        
        for group in redundant_groups:
            # 简单策略：保留第一个特征，其他标记为冗余
            keep_feature = group[0]
            redundant_features = group[1:]
            
            recommendations.append({
                'group': group,
                'keep': keep_feature,
                'remove': redundant_features,
                'group_size': len(group)
            })
            
            all_redundant_features.update(redundant_features)
        
        print(f"  🔗 发现 {len(redundant_groups)} 个冗余特征组")
        print(f"  🗑️ 建议移除 {len(all_redundant_features)} 个冗余特征")
        
        return {
            'redundant_groups': redundant_groups,
            'recommendations': recommendations,
            'total_redundant_features': len(all_redundant_features),
            'redundant_features_list': list(all_redundant_features)
        }
    
    def vif_analysis(self, df, feature_cols):
        """方差膨胀因子(VIF)分析"""
        print("📊 执行VIF分析...")
        
        # 限制特征数量以避免计算过慢
        if len(feature_cols) > self.config['max_features_for_vif']:
            # 选择方差最大的特征进行VIF分析
            feature_vars = df[feature_cols].var().sort_values(ascending=False)
            selected_features = feature_vars.head(self.config['max_features_for_vif']).index.tolist()
            print(f"  📉 限制VIF分析特征数: {len(selected_features)}")
        else:
            selected_features = feature_cols
        
        df_vif = df[selected_features].dropna()
        
        if len(df_vif) == 0:
            return {'error': 'VIF分析数据为空'}
        
        # 标准化数据
        scaler = StandardScaler()
        df_scaled = pd.DataFrame(
            scaler.fit_transform(df_vif),
            columns=df_vif.columns
        )
        
        # 计算VIF
        vif_results = []
        high_vif_features = []
        
        try:
            for i, feature in enumerate(df_scaled.columns):
                vif_value = variance_inflation_factor(df_scaled.values, i)
                vif_results.append({
                    'feature': feature,
                    'vif': float(vif_value) if not np.isnan(vif_value) else 0.0
                })
                
                if vif_value > self.config['vif_threshold']:
                    high_vif_features.append(feature)
            
            # 按VIF值排序
            vif_results.sort(key=lambda x: x['vif'], reverse=True)
            
            vif_stats = {
                'total_features_analyzed': len(vif_results),
                'high_vif_features_count': len(high_vif_features),
                'mean_vif': float(np.mean([r['vif'] for r in vif_results])),
                'max_vif': float(max([r['vif'] for r in vif_results])),
                'vif_threshold': self.config['vif_threshold']
            }
            
            print(f"  📈 高VIF特征数: {len(high_vif_features)}")
            print(f"  📊 平均VIF: {vif_stats['mean_vif']:.2f}")
            print(f"  📉 最大VIF: {vif_stats['max_vif']:.2f}")
            
            return {
                'vif_results': vif_results,
                'high_vif_features': high_vif_features,
                'vif_stats': vif_stats
            }
            
        except Exception as e:
            print(f"  ❌ VIF计算失败: {e}")
            return {'error': str(e)}
    
    def generate_correlation_heatmap(self, corr_matrix, top_n=50):
        """生成相关性热力图"""
        print("🎨 生成相关性热力图...")
        
        # 选择方差最大的前N个特征
        if len(corr_matrix.columns) > top_n:
            feature_vars = corr_matrix.var().sort_values(ascending=False)
            top_features = feature_vars.head(top_n).index.tolist()
            corr_subset = corr_matrix.loc[top_features, top_features]
        else:
            corr_subset = corr_matrix
        
        # 创建热力图
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(corr_subset, dtype=bool))
        
        sns.heatmap(
            corr_subset,
            mask=mask,
            annot=False,
            cmap='RdBu_r',
            center=0,
            square=True,
            fmt='.2f',
            cbar_kws={'shrink': 0.8}
        )
        
        plt.title(f'特征相关性热力图 (Top {len(corr_subset.columns)} 特征)', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        # 保存图片
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        heatmap_file = os.path.join(self.output_dir, f'correlation_heatmap_{timestamp}.png')
        plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  💾 热力图已保存: {heatmap_file}")
        return heatmap_file

    def run_comprehensive_analysis(self):
        """运行全面的相关性和多重共线性分析"""
        print("🚀 开始相关性和多重共线性分析...")

        # 加载数据
        df, feature_cols = self.load_sample_data()

        # 相关性分析
        correlation_results = self.correlation_analysis(df, feature_cols)

        # 识别冗余特征
        redundancy_results = self.identify_redundant_features(correlation_results)

        # VIF分析
        vif_results = self.vif_analysis(df, feature_cols)

        # 生成热力图
        heatmap_file = self.generate_correlation_heatmap(correlation_results['correlation_matrix'])

        # 整合结果
        comprehensive_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'data_info': {
                'sample_size': len(df),
                'total_features': len(feature_cols),
                'analysis_features': len(df.columns)
            },
            'correlation_analysis': {
                'stats': correlation_results['correlation_stats'],
                'high_correlation_pairs': correlation_results['high_correlation_pairs'],
                'moderate_correlation_pairs': correlation_results['moderate_correlation_pairs']
            },
            'redundancy_analysis': redundancy_results,
            'vif_analysis': vif_results,
            'visualizations': {
                'correlation_heatmap': heatmap_file
            }
        }

        self.results = comprehensive_results
        return comprehensive_results

    def generate_detailed_report(self, results):
        """生成详细分析报告"""
        report = f"""
# AG特征相关性和多重共线性分析报告

## 分析概览

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据目录**: {self.data_dir}
**样本数量**: {results['data_info']['sample_size']:,}
**分析特征数**: {results['data_info']['total_features']}

## 相关性分析结果

### 基本统计
- **特征对总数**: {results['correlation_analysis']['stats']['total_feature_pairs']:,}
- **高相关性对数** (≥{self.config['high_correlation_threshold']}): {results['correlation_analysis']['stats']['high_correlation_pairs']}
- **中等相关性对数** (≥{self.config['moderate_correlation_threshold']}): {results['correlation_analysis']['stats']['moderate_correlation_pairs']}
- **平均绝对相关性**: {results['correlation_analysis']['stats']['mean_abs_correlation']:.3f}
- **最大相关性**: {results['correlation_analysis']['stats']['max_correlation']:.3f}

### 高相关性特征对 (Top 20)
"""

        high_corr_pairs = results['correlation_analysis']['high_correlation_pairs'][:20]
        for i, pair in enumerate(high_corr_pairs, 1):
            report += f"{i}. **{pair['feature1']}** ↔ **{pair['feature2']}**: {pair['correlation']:.3f}\n"

        report += f"""

## 冗余特征分析

### 冗余特征组
发现 **{results['redundancy_analysis']['total_redundant_features']}** 个冗余特征，分布在 **{len(results['redundancy_analysis']['redundant_groups'])}** 个特征组中。

"""

        for i, rec in enumerate(results['redundancy_analysis']['recommendations'][:10], 1):
            report += f"**组 {i}** (共{rec['group_size']}个特征):\n"
            report += f"- 保留: `{rec['keep']}`\n"
            report += f"- 移除: {', '.join([f'`{f}`' for f in rec['remove']])}\n\n"

        if 'vif_analysis' in results and 'error' not in results['vif_analysis']:
            vif_results = results['vif_analysis']
            report += f"""

## 多重共线性分析 (VIF)

### VIF统计
- **分析特征数**: {vif_results['vif_stats']['total_features_analyzed']}
- **高VIF特征数** (>{self.config['vif_threshold']}): {vif_results['vif_stats']['high_vif_features_count']}
- **平均VIF**: {vif_results['vif_stats']['mean_vif']:.2f}
- **最大VIF**: {vif_results['vif_stats']['max_vif']:.2f}

### 高VIF特征 (Top 15)
"""

            high_vif_features = [r for r in vif_results['vif_results'] if r['vif'] > self.config['vif_threshold']][:15]
            for i, vif_info in enumerate(high_vif_features, 1):
                report += f"{i}. **{vif_info['feature']}**: VIF = {vif_info['vif']:.2f}\n"

        report += f"""

## 推荐建议

### 特征筛选建议

#### 1. 基于高相关性的特征筛选
- 建议移除 **{results['redundancy_analysis']['total_redundant_features']}** 个冗余特征
- 这些特征与其他特征的相关性超过 {self.config['high_correlation_threshold']}
- 移除后可以减少特征维度，提高模型训练效率

#### 2. 基于多重共线性的特征筛选
"""

        if 'vif_analysis' in results and 'error' not in results['vif_analysis']:
            high_vif_count = results['vif_analysis']['vif_stats']['high_vif_features_count']
            report += f"- 发现 **{high_vif_count}** 个高VIF特征\n"
            report += f"- 建议优先移除VIF > {self.config['vif_threshold']} 的特征\n"
        else:
            report += "- VIF分析未能完成，建议手动检查多重共线性\n"

        report += f"""

#### 3. 综合筛选策略
1. **优先级1**: 移除高相关性冗余特征
2. **优先级2**: 移除高VIF特征
3. **优先级3**: 结合业务知识进行最终筛选

### 特征工程建议
1. **主成分分析**: 对高相关性特征组进行PCA降维
2. **特征组合**: 将相关特征进行加权组合
3. **正则化**: 使用L1/L2正则化自动进行特征选择

## 质量保证

### 分析方法
- **相关性分析**: 使用皮尔逊相关系数
- **多重共线性**: 使用方差膨胀因子(VIF)
- **冗余检测**: 基于相关性阈值的图算法

### 分析限制
- 分析基于单个代表性数据文件
- VIF分析限制在前{self.config['max_features_for_vif']}个特征
- 采样分析可能不完全代表全量数据特征

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report

    def save_results(self, results, output_prefix='ag_correlation_analysis'):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果
        full_results_file = os.path.join(self.output_dir, f'{output_prefix}_full_{timestamp}.json')
        with open(full_results_file, 'w', encoding='utf-8') as f:
            # 移除不能序列化的对象
            results_copy = results.copy()
            if 'correlation_analysis' in results_copy:
                # 移除correlation_matrix，因为它是DataFrame
                if 'correlation_matrix' in results_copy['correlation_analysis']:
                    del results_copy['correlation_analysis']['correlation_matrix']

            json.dump(results_copy, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 完整结果已保存: {full_results_file}")

        # 保存冗余特征列表
        redundant_features_file = os.path.join(self.output_dir, f'{output_prefix}_redundant_features_{timestamp}.json')
        redundant_data = {
            'redundant_features_list': results['redundancy_analysis']['redundant_features_list'],
            'redundant_groups': results['redundancy_analysis']['redundant_groups'],
            'recommendations': results['redundancy_analysis']['recommendations']
        }
        with open(redundant_features_file, 'w', encoding='utf-8') as f:
            json.dump(redundant_data, f, ensure_ascii=False, indent=2)
        print(f"✅ 冗余特征列表已保存: {redundant_features_file}")

        # 保存高相关性特征对CSV
        if results['correlation_analysis']['high_correlation_pairs']:
            high_corr_df = pd.DataFrame(results['correlation_analysis']['high_correlation_pairs'])
            high_corr_csv = os.path.join(self.output_dir, f'{output_prefix}_high_correlation_pairs_{timestamp}.csv')
            high_corr_df.to_csv(high_corr_csv, index=False, encoding='utf-8-sig')
            print(f"✅ 高相关性特征对已保存: {high_corr_csv}")

        # 保存VIF结果CSV
        if 'vif_analysis' in results and 'vif_results' in results['vif_analysis']:
            vif_df = pd.DataFrame(results['vif_analysis']['vif_results'])
            vif_csv = os.path.join(self.output_dir, f'{output_prefix}_vif_results_{timestamp}.csv')
            vif_df.to_csv(vif_csv, index=False, encoding='utf-8-sig')
            print(f"✅ VIF结果已保存: {vif_csv}")

        return {
            'full_results': full_results_file,
            'redundant_features': redundant_features_file,
            'high_correlation_pairs': high_corr_csv if results['correlation_analysis']['high_correlation_pairs'] else None,
            'vif_results': vif_csv if 'vif_analysis' in results and 'vif_results' in results['vif_analysis'] else None
        }


def main():
    """主函数"""
    print("🔗 AG特征相关性和多重共线性分析工具")
    print("=" * 60)

    # 创建分析器
    analyzer = CorrelationMulticollinearityAnalyzer(
        data_dir='f:/featdata/ag/',
        output_dir='./correlation_analysis_results/'
    )

    try:
        # 运行全面分析
        results = analyzer.run_comprehensive_analysis()

        # 保存结果
        saved_files = analyzer.save_results(results)

        # 生成详细报告
        detailed_report = analyzer.generate_detailed_report(results)

        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(analyzer.output_dir, f'ag_correlation_analysis_report_{timestamp}.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(detailed_report)

        print(f"\n🎉 分析完成!")
        print(f"📊 详细报告: {report_file}")
        print(f"📈 完整结果: {saved_files['full_results']}")
        print(f"🔗 冗余特征: {saved_files['redundant_features']}")
        if saved_files['high_correlation_pairs']:
            print(f"📋 高相关性对: {saved_files['high_correlation_pairs']}")
        if saved_files['vif_results']:
            print(f"📊 VIF结果: {saved_files['vif_results']}")

        # 打印摘要
        print(f"\n📋 分析摘要:")
        print(f"  总特征数: {results['data_info']['total_features']}")
        print(f"  高相关性对: {results['correlation_analysis']['stats']['high_correlation_pairs']}")
        print(f"  冗余特征数: {results['redundancy_analysis']['total_redundant_features']}")
        if 'vif_analysis' in results and 'vif_stats' in results['vif_analysis']:
            print(f"  高VIF特征数: {results['vif_analysis']['vif_stats']['high_vif_features_count']}")

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
