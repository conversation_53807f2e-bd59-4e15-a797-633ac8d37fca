
# AG特征数据质量分析报告

## 分析概览

**分析时间**: 2025-07-19 09:47:08
**数据目录**: f:/featdata/ag/
**总特征数**: 227

## 质量评估摘要

### 推荐分布
- **保留特征 (KEEP)**: 221 (97.4%)
- **过滤特征 (FILTER)**: 0 (0.0%)
- **需审查特征 (REVIEW)**: 6 (2.6%)

### 质量分布
- **高质量特征 (≥0.8)**: 80
- **中等质量特征 (0.4-0.8)**: 147
- **低质量特征 (<0.4)**: 0

**平均质量评分**: 0.786

## 最佳特征 (Top 10)

1. **MACD_DIFF_2** - 评分: 1.000
2. **KDJ_K_1** - 评分: 1.000
3. **MACD_DEA_2** - 评分: 1.000
4. **MACD_DEA_1** - 评分: 1.000
5. **MACD_DIFF_1** - 评分: 1.000
6. **KDJ_K_2** - 评分: 1.000
7. **LR_SLOPE_SLOW_1** - 评分: 1.000
8. **LR_SLOPE_SLOW_2** - 评分: 1.000
9. **MAMA_FAST_1** - 评分: 0.999
10. **DEMA_FAST_1** - 评分: 0.999


## 最差特征 (Bottom 10)

1. **SQUEEZE_SIGNAL_1** - 评分: 0.631
2. **SQUEEZE_SIGNAL_2** - 评分: 0.631
3. **SQUEEZE_1** - 评分: 0.614
4. **SQUEEZE_2** - 评分: 0.614
5. **SHORT_TERM_LOW_2** - 评分: 0.579
6. **SHORT_TERM_LOW_1** - 评分: 0.579
7. **SHORT_TERM_HIGH_1** - 评分: 0.500
8. **TREND_LOWEST_2** - 评分: 0.500
9. **SHORT_TERM_HIGH_2** - 评分: 0.500
10. **TREND_LOWEST_1** - 评分: 0.500


## 分析方法说明

### 评估维度
1. **缺失值分析**: 检查特征的缺失率，阈值: 0.3
2. **方差分析**: 检测零方差或低方差特征
3. **分布分析**: 分析特征值分布的合理性
4. **目标相关性**: 评估特征与目标变量的相关性
5. **稳定性分析**: 检验特征在不同时间段的稳定性
6. **异常值分析**: 检测异常值比例

### 评分机制
- **总评分**: 各维度加权平均 (0-1分)
- **推荐标准**:
  - KEEP: 总评分 ≥ 0.6
  - REVIEW: 0.3 ≤ 总评分 < 0.6
  - FILTER: 总评分 < 0.3

## 使用建议

### 高质量特征 (推荐保留)
建议在模型训练中优先使用评分较高的特征，这些特征具有：
- 较低的缺失率
- 合理的方差
- 与目标变量有一定相关性
- 时序稳定性好
- 异常值比例可控

### 需要过滤的特征
以下类型的特征建议过滤：
- 缺失率过高 (>0.3)
- 零方差或接近零方差
- 与目标变量相关性极低
- 时序不稳定
- 异常值比例过高

### 需要审查的特征
中等评分的特征需要进一步分析：
- 可能需要特征工程处理
- 可能在特定条件下有用
- 建议结合业务知识判断

---

*报告生成时间: 2025-07-19 09:47:08*
