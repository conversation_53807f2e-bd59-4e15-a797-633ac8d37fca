e:
cd e:\lab\RoboQuant\pylab

@REM python ./pyqlab/data/datatools/SimulationServer.py ^
@REM --sel_fut MAIN_TRD_FUT ^
@REM --data_source tick ^
@REM --market fut

@REM python ./pyqlab/data/datatools/SimulationServer.py ^
@REM --data_path f:/hqdata/tsdb ^
@REM --sel_fut MAIN_TOP_FUT ^
@REM --market fut ^
@REM --block_name top ^
@REM --period min1

@REM python ./pyqlab/data/datatools/SimulationServer.py ^
@REM --data_path f:/hqdata/tsdb ^
@REM --sel_fut MAIN_SF_FUT ^
@REM --market fut ^
@REM --block_name sf ^
@REM --period min1

@REM python ./pyqlab/data/datatools/SimulationServer.py ^
@REM --data_path f:/hqdata/tsdb ^
@REM --sel_fut MAIN_SF_FUT ^
@REM --market fut ^
@REM --block_name ic ^
@REM --period min1

python ./pyqlab/data/datatools/SimulationServer.py ^
--data_path f:/hqdata/tsdb ^
--sel_fut MAIN_SEL_FUT ^
--market fut ^
--block_name ag ^
--period min1