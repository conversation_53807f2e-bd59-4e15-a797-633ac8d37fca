{
  "unstable_features": [
    "TRANGE_1",
    "NATR_TL_1",
    "ATR_1",
    "SQUEEZE_GAP_1",
    "OPEN_1",
    "SHORT_TERM_HIGH_1",
    "change",
    "NATR_1",
    "T3_SLOW_2",
    "BAND_MDL_2",
    "BOLL_MID_2",
    "HYO_KIJUN_SEN_2",
    "KDJ_D_2",
    "KAMA_SLOW_2",
    "MA_SLOW_2",
    "SQUEEZE_MDL_2",
    "SQUEEZE_KC_UPL_2",
    "TATR_1",
    "TRIMA_SLOW_2",
    "SQUEEZE_KC_DWL_2",
    "TRIX_SLOW_2",
    "BAND_DWL_2",
    "LR_SLOPE_SLOW_THRESHOLD_1",
    "LR_SLOPE_SLOW_THRESHOLD_2",
    "STDDEV_THRESHOLD_1",
    "STDDEV_THRESHOLD_2",
    "SQUEEZE_ZERO_BARS_1",
    "SQUEEZE_ZERO_BARS_2",
    "BOLL_UP_2",
    "BOLL_DOWN_2",
    "TL_THRESHOLD_1",
    "TL_THRESHOLD_2",
    "BAND_GRADIENT_THRESHOLD_2",
    "BAND_GRADIENT_THRESHOLD_1",
    "SQUEEZE_GAP_SLOW_2",
    "BAND_UPL_2",
    "MOMENTUM_THRESHOLD_1",
    "MOMENTUM_THRESHOLD_2",
    "HYO_CROSS_BARS_2",
    "HYO_CROSS_BARS_1",
    "EMA_SLOW_2",
    "TL_SLOW_2",
    "LR_SLOPE_SLOW_2",
    "SQUEEZE_GAP_THRESHOLD_2",
    "SQUEEZE_GAP_THRESHOLD_1",
    "SQUEEZE_BAND_UPL_2",
    "MACD_DEA_2",
    "SQUEEZE_BAND_DWL_2",
    "MAMA_SLOW_2",
    "T3_FAST_2",
    "KDJ_K_2",
    "BAND_WIDTH_2",
    "BAND_WIDTH_1",
    "SHORT_TERM_HIGH_2",
    "HYO_TENKAN_SEN_2",
    "ADXR_2",
    "DEMA_SLOW_2",
    "TATR_2",
    "ADX_2",
    "SQUEEZE_GAP_FAST_2",
    "KAMA_FAST_2",
    "BAND_GRADIENT_2",
    "LR_SLOPE_FAST_THRESHOLD_1",
    "LR_SLOPE_FAST_THRESHOLD_2",
    "MOMENTUM_2",
    "TSF_2",
    "MA_FAST_2",
    "TRIMA_FAST_2",
    "HYO_KIJUN_SEN_1",
    "TEMA_SLOW_2",
    "TL_FAST_2",
    "MACD_DIFF_2",
    "TREND_HLR_2",
    "TREND_HLR_1",
    "NATR_2",
    "EMA_FAST_2",
    "AD_2",
    "TREND_LOWEST_1",
    "TREND_LOWEST_2",
    "SAR_2",
    "OBV_2",
    "T3_SLOW_1",
    "BAND_MDL_1",
    "BOLL_MID_1",
    "TREND_BARS_1",
    "TREND_BARS_2",
    "MAMA_FAST_2",
    "SQUEEZE_KC_UPL_1",
    "SQUEEZE_KC_DWL_1",
    "STDDEV_SLOW_2",
    "SQUEEZE_GAP_2",
    "SQUEEZE_SIGNAL_1",
    "SQUEEZE_SIGNAL_2",
    "DEMA_FAST_2",
    "SQUEEZE_MDL_1",
    "MA_SLOW_1",
    "HYO_TENKAN_SEN_1",
    "KDJ_D_1",
    "SHORT_TERM_LOW_2",
    "TRIX_SLOW_1"
  ],
  "stability_scores": {
    "TRANGE_1": {
      "stability_score": 0.2180746469987996,
      "mean_cv": 1.7235721849428962,
      "std_cv": 1.6866188421606156,
      "median_cv": 0.17539475172159177,
      "is_stable": 